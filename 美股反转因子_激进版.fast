# 美股反转因子 - 激进版
# 基于用户发现的有效信号因子，最激进的低相关性方案

## 原始有效因子
# group_zscore(-ts_mean(close / ts_delay(close, 1) - 1, 20), bucket(rank(cap), range="0, 1, 0.1"))

## 激进低相关性版本

### 方案A：完全不同的价格字段（最推荐）
```fast
# 使用high替代close，完全不同的价格视角
# 短期动量（5天）
short_momentum = -ts_mean(high / ts_delay(high, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(high / ts_delay(high, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(high / ts_delay(high, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案B：使用low价格
```fast
# 使用low替代close，完全不同的价格视角
# 短期动量（5天）
short_momentum = -ts_mean(low / ts_delay(low, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(low / ts_delay(low, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(low / ts_delay(low, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案C：使用returns字段
```fast
# 直接使用returns字段，避免价格计算
# 短期动量（5天）
short_momentum = -ts_mean(returns, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(returns, 20)
# 长期动量（60天）
long_momentum = -ts_mean(returns, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案D：使用ts_rank替代ts_mean
```fast
# 使用ts_rank替代ts_mean，完全不同的计算方法
# 短期动量（5天）
short_momentum = -ts_rank(close / ts_delay(close, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_rank(close / ts_delay(close, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_rank(close / ts_delay(close, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案E：使用ts_quantile替代ts_mean
```fast
# 使用ts_quantile替代ts_mean，完全不同的计算方法
# 短期动量（5天）
short_momentum = -ts_quantile(close / ts_delay(close, 1) - 1, 5, 0.5)
# 中期动量（20天）
medium_momentum = -ts_quantile(close / ts_delay(close, 1) - 1, 20, 0.5)
# 长期动量（60天）
long_momentum = -ts_quantile(close / ts_delay(close, 1) - 1, 60, 0.5)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案F：使用group_scale替代group_zscore
```fast
# 使用group_scale替代group_zscore，完全不同的标准化方法
# 短期动量（5天）
short_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组缩放
final_factor = group_scale(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案G：复合激进替换（最激进）
```fast
# 多重激进替换组合，最大程度降低相关性
# 使用high替代close，ts_rank替代ts_mean，group_scale替代group_zscore
# 短期动量（5天）
short_momentum = -ts_rank(high / ts_delay(high, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_rank(high / ts_delay(high, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_rank(high / ts_delay(high, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组缩放
final_factor = group_scale(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

## 经济学理论支持
# 1. 反转效应：短期价格过度反应后的修正
# 2. 市值效应：不同市值股票的反转特征差异
# 3. 多时间尺度：捕捉不同时间尺度的反转机会

## 因子特性
# - 反转信号：捕捉价格反转机会
# - 市值中性：控制市值效应影响
# - 多时间尺度：捕捉不同反转周期
# - 激进低相关性：通过完全不同的方法大幅降低相关性

## 使用建议
# 方案A：使用high替代close，完全不同的价格视角（最推荐）
# 方案B：使用low替代close，完全不同的价格视角
# 方案C：直接使用returns字段，避免价格计算
# 方案D：使用ts_rank替代ts_mean，完全不同的计算方法
# 方案E：使用ts_quantile替代ts_mean，完全不同的计算方法
# 方案F：使用group_scale替代group_zscore，完全不同的标准化方法
# 方案G：复合激进替换，最大程度降低相关性（最激进） 