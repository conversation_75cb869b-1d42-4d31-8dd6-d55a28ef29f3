# Mamba adoption

We've been very happy to see Mamba being adopted by many organizations
and research labs to speed up their training / inference.
This page contains a partial list of places where Mamba is being used.
If you'd like to add links to your organization / product / codebase, please open a
PR or email us. We'd very much like to hear from you!

## Large language models and multi-modal models

- [Tencent's Hunyuan-TurboS (560B)](https://arxiv.org/abs/2505.15431)

- [Nvidia Nemotron-H (8B, 47B, 56B)](https://research.nvidia.com/labs/adlr/nemotronh/)

- [AI21 Jamba (398B)](https://www.ai21.com/blog/announcing-jamba-model-family/)

- [TII Falcon-H1 (34B)](https://falconllm.tii.ae/falcon-h1.html)

- [IBM Bamba (9B)](https://research.ibm.com/blog/bamba-ssm-transformer-model)

- [Mistral's Codestral (7B)](https://mistral.ai/news/codestral-mamba)

- [Nvidia Mamba-2 Hybrid (8B)](https://arxiv.org/abs/2406.07887)

- [Microsoft Samba (4B)](https://arxiv.org/abs/2406.07522v1)

- [TII Falcon-Mamba (7B)](https://falconllm.tii.ae/tii-releases-first-sslm-with-falcon-mamba-7b.html)

## Inference frameworks

- vLLM

- Nvidia's TensorRT-LLM

## Hardware

- Nvidia GPUs

- [AMD GPUs](https://rocm.blogs.amd.com/artificial-intelligence/mamba/README.html)

- [AWS Trainium 2](https://awsdocs-neuron.readthedocs-hosted.com/en/latest/general/nki/tutorials/fused_mamba.html)


