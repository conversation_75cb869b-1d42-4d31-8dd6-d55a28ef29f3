[project]
name = "mamba_ssm"
description = "Mamba state-space model"
readme = "README.md"
authors = [
    { name = "<PERSON> Dao", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" }
]
requires-python = ">= 3.9"
dynamic = ["version"]
license = { file = "LICENSE" }  # Include a LICENSE file in your repo
keywords = ["cuda", "pytorch", "state-space model"]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: Unix"
]
dependencies = [
    "torch",
    "triton",
    "ninja",
    "einops",
    "transformers",
    "packaging",
    "setuptools>=61.0.0",
]
[project.urls]
Repository = "https://github.com/state-spaces/mamba"

[project.optional-dependencies]
causal-conv1d = [
    "causal-conv1d>=1.2.0"
]
dev = [
    "pytest"
]


[build-system]
requires = [
    "setuptools>=61.0.0",
    "wheel",
    "torch",
    "packaging",
    "ninja",
]
build-backend = "setuptools.build_meta"
