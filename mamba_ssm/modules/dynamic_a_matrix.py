"""
Dynamic A Matrix Modulation for ProFoNet v2
动态状态转移矩阵A的调制实现

核心功能：
1. 根据选通值动态调制A矩阵
2. 保持数值稳定性
3. 支持梯度反向传播
4. 提供备份和恢复机制
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, Dict
import time
import warnings


class DynamicAMatrixModulator(nn.Module):
    """
    动态状态转移矩阵A的调制器
    
    实现ProFoNet v2中的核心创新：使状态转移矩阵A根据输入内容动态调制
    A_modulated = g_t * A_base + (1 - g_t) * I
    """
    
    def __init__(self, d_inner: int, d_state: int, device=None, dtype=None):
        super().__init__()
        self.d_inner = d_inner
        self.d_state = d_state
        
        # 基础A矩阵初始化（S4D方式）
        A = torch.arange(1, d_state + 1, dtype=torch.float32, device=device)
        A = A.repeat(d_inner, 1)  # (d_inner, d_state)
        A_log = torch.log(A)
        self.A_log = nn.Parameter(A_log)
        self.A_log._no_weight_decay = True
        
        # 单位矩阵（用于调制）
        self.register_buffer('identity_matrix', 
                           torch.eye(d_state, dtype=torch.float32, device=device))
        
        # 备份机制
        self._backup_A_log = None
        self._backup_enabled = True
        self._backup_timestamp = None
        
        # 数值稳定性参数
        self.eps = 1e-8
        self.max_value_threshold = 1e6
        
    def get_base_A_matrix(self) -> torch.Tensor:
        """获取基础A矩阵（负指数形式）"""
        return -torch.exp(self.A_log.float())
    
    def modulate_A_matrix(self, gating_values: torch.Tensor) -> torch.Tensor:
        """
        动态调制A矩阵
        
        Args:
            gating_values: 选通值，支持以下形状：
                - (batch_size, 1): 全局选通值
                - (batch_size, d_inner): 每个维度独立选通值
                - (batch_size, sequence_length): 序列级选通值（会取平均）
                
        Returns:
            A_modulated: 调制后的A矩阵 (batch_size, d_inner, d_state)
        """
        # 获取基础A矩阵
        A_base = self.get_base_A_matrix()  # (d_inner, d_state)
        batch_size = gating_values.shape[0]
        
        # 处理不同的选通值输入格式
        g_processed = self._process_gating_values(gating_values)  # (batch_size, d_inner)
        
        # 扩展维度进行调制
        g_expanded = g_processed.unsqueeze(-1)  # (batch_size, d_inner, 1)
        A_base_expanded = A_base.unsqueeze(0).expand(batch_size, -1, -1)  # (batch, d_inner, d_state)
        
        # 获取对角单位矩阵的对角元素
        I_diag = self.identity_matrix.diagonal()  # (d_state,)
        I_expanded = I_diag.unsqueeze(0).unsqueeze(0).expand(batch_size, self.d_inner, -1)
        
        # 动态调制：A_modulated = g * A_base + (1 - g) * I
        A_modulated = g_expanded * A_base_expanded + (1 - g_expanded) * I_expanded
        
        # 数值稳定性检查和修正
        A_modulated = self._ensure_numerical_stability(A_modulated)
        
        return A_modulated
    
    def _process_gating_values(self, gating_values: torch.Tensor) -> torch.Tensor:
        """
        处理不同格式的选通值输入
        
        Returns:
            processed_gating: (batch_size, d_inner)
        """
        batch_size = gating_values.shape[0]
        
        if gating_values.dim() == 2:
            if gating_values.shape[1] == 1:
                # 全局选通值，广播到所有维度
                return gating_values.expand(batch_size, self.d_inner)
            elif gating_values.shape[1] == self.d_inner:
                # 每个维度独立的选通值
                return gating_values
            else:
                # 序列级选通值，取平均后广播
                g_avg = gating_values.mean(dim=1, keepdim=True)  # (batch_size, 1)
                return g_avg.expand(batch_size, self.d_inner)
        elif gating_values.dim() == 1:
            # 一维选通值，广播到所有维度
            return gating_values.unsqueeze(1).expand(batch_size, self.d_inner)
        else:
            raise ValueError(f"Unsupported gating_values shape: {gating_values.shape}")
    
    def _ensure_numerical_stability(self, A_modulated: torch.Tensor) -> torch.Tensor:
        """确保数值稳定性"""
        # 检查NaN和Inf
        if torch.isnan(A_modulated).any():
            warnings.warn("NaN detected in A_modulated, replacing with base A matrix")
            A_base = self.get_base_A_matrix()
            A_modulated = A_base.unsqueeze(0).expand_as(A_modulated)
        
        if torch.isinf(A_modulated).any():
            warnings.warn("Inf detected in A_modulated, clipping values")
            A_modulated = torch.clamp(A_modulated, -self.max_value_threshold, self.max_value_threshold)
        
        # 检查过大的值
        if (A_modulated.abs() > self.max_value_threshold).any():
            warnings.warn(f"Large values detected in A_modulated (max: {A_modulated.abs().max():.2e})")
            A_modulated = torch.clamp(A_modulated, -self.max_value_threshold, self.max_value_threshold)
        
        return A_modulated
    
    def create_backup(self) -> None:
        """创建A矩阵参数的备份"""
        if self._backup_enabled:
            self._backup_A_log = self.A_log.data.clone().detach()
            self._backup_timestamp = time.time()
            print(f"[DynamicAMatrix] Backup created at {time.strftime('%H:%M:%S')}")
    
    def restore_from_backup(self) -> bool:
        """
        从备份恢复A矩阵参数
        
        Returns:
            bool: 是否成功恢复
        """
        if self._backup_A_log is not None:
            self.A_log.data.copy_(self._backup_A_log)
            backup_time = time.strftime('%H:%M:%S', time.localtime(self._backup_timestamp))
            print(f"[DynamicAMatrix] Restored from backup created at {backup_time}")
            return True
        else:
            warnings.warn("No backup available for restoration")
            return False
    
    def enable_backup(self, enabled: bool = True) -> None:
        """启用或禁用备份功能"""
        self._backup_enabled = enabled
        if enabled:
            print("[DynamicAMatrix] Backup enabled")
        else:
            print("[DynamicAMatrix] Backup disabled")
    
    def get_modulation_stats(self, gating_values: torch.Tensor) -> Dict[str, float]:
        """
        获取调制统计信息，用于监控和调试
        
        Returns:
            dict: 包含各种统计指标的字典
        """
        with torch.no_grad():
            A_base = self.get_base_A_matrix()
            A_modulated = self.modulate_A_matrix(gating_values)
            g_processed = self._process_gating_values(gating_values)
            
            # 计算调制强度
            modulation_diff = (A_modulated - A_base.unsqueeze(0)).abs()
            
            stats = {
                'base_A_mean': A_base.mean().item(),
                'base_A_std': A_base.std().item(),
                'base_A_min': A_base.min().item(),
                'base_A_max': A_base.max().item(),
                'modulated_A_mean': A_modulated.mean().item(),
                'modulated_A_std': A_modulated.std().item(),
                'gating_mean': g_processed.mean().item(),
                'gating_std': g_processed.std().item(),
                'gating_min': g_processed.min().item(),
                'gating_max': g_processed.max().item(),
                'modulation_strength_mean': modulation_diff.mean().item(),
                'modulation_strength_max': modulation_diff.max().item(),
                'effective_gating_ratio': (g_processed > 0.5).float().mean().item(),
            }
        return stats
    
    def reset_parameters(self) -> None:
        """重置参数到初始状态"""
        # 重新初始化A_log
        A = torch.arange(1, self.d_state + 1, dtype=torch.float32, device=self.A_log.device)
        A = A.repeat(self.d_inner, 1)
        A_log = torch.log(A)
        self.A_log.data.copy_(A_log)
        print("[DynamicAMatrix] Parameters reset to initial state")


class SimpleGatingNetwork(nn.Module):
    """
    简单的选通网络，用于生成选通值
    
    这是一个轻量级的网络，用于从输入特征生成选通值
    """
    
    def __init__(self, input_dim: int, hidden_dim: int = 64, dropout: float = 0.1):
        super().__init__()
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # 确保输出在[0,1]范围内
        )
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化网络权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入特征 (batch_size, input_dim) 或 (batch_size, seq_len, input_dim)
            
        Returns:
            gating_values: 选通值 (batch_size, 1) 或 (batch_size, seq_len, 1)
        """
        original_shape = x.shape
        
        # 处理不同的输入维度
        if x.dim() == 3:
            # (batch_size, seq_len, input_dim) -> (batch_size * seq_len, input_dim)
            batch_size, seq_len, input_dim = x.shape
            x = x.view(-1, input_dim)
            
            # 前向传播
            gating = self.network(x)  # (batch_size * seq_len, 1)
            
            # 恢复原始形状
            gating = gating.view(batch_size, seq_len, 1)
        else:
            # (batch_size, input_dim)
            gating = self.network(x)  # (batch_size, 1)
        
        return gating


class AutoBackupManager:
    """
    自动备份管理器
    
    用于在训练过程中自动创建和管理A矩阵的备份
    """
    
    def __init__(self, modulator: DynamicAMatrixModulator, backup_interval: int = 100):
        self.modulator = modulator
        self.backup_interval = backup_interval
        self.step_count = 0
        self.backup_history = []
        self.max_backups = 10  # 最多保留的备份数量
    
    def step(self) -> None:
        """每次训练步骤调用"""
        self.step_count += 1
        if self.step_count % self.backup_interval == 0:
            self.create_checkpoint()
    
    def create_checkpoint(self) -> None:
        """创建检查点"""
        checkpoint = {
            'step': self.step_count,
            'A_log': self.modulator.A_log.data.clone().detach(),
            'timestamp': time.time()
        }
        
        self.backup_history.append(checkpoint)
        
        # 限制备份数量
        if len(self.backup_history) > self.max_backups:
            self.backup_history.pop(0)
        
        print(f"[AutoBackup] Checkpoint created at step {self.step_count}")
    
    def restore_checkpoint(self, step: int = -1) -> bool:
        """
        恢复到指定检查点
        
        Args:
            step: 检查点索引，-1表示最新的检查点
            
        Returns:
            bool: 是否成功恢复
        """
        if not self.backup_history:
            print("[AutoBackup] No checkpoints available")
            return False
        
        try:
            checkpoint = self.backup_history[step]
            self.modulator.A_log.data.copy_(checkpoint['A_log'])
            backup_time = time.strftime('%H:%M:%S', time.localtime(checkpoint['timestamp']))
            print(f"[AutoBackup] Restored to checkpoint at step {checkpoint['step']} (created at {backup_time})")
            return True
        except IndexError:
            print(f"[AutoBackup] Invalid checkpoint index: {step}")
            return False
    
    def list_checkpoints(self) -> None:
        """列出所有可用的检查点"""
        if not self.backup_history:
            print("[AutoBackup] No checkpoints available")
            return
        
        print("[AutoBackup] Available checkpoints:")
        for i, checkpoint in enumerate(self.backup_history):
            timestamp = time.strftime('%H:%M:%S', time.localtime(checkpoint['timestamp']))
            print(f"  [{i}] Step {checkpoint['step']} - {timestamp}")
