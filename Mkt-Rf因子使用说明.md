# Mkt-Rf因子使用说明

## 概述
本文档提供了使用MCP工具和fast expression语法构建美股Mkt-Rf因子的完整方案。

## 数据库信息
- **数据源**: USA 1 TOP3000
- **数据字段**: close, returns, cap, volume, vwap等
- **操作符**: ts_mean, ts_std_dev, ts_zscore, rank, ts_sum等

## 核心实现

### 推荐实现（Z-score标准化）
```fast
# 1. 计算市值加权收益率
weighted_returns = returns * cap / ts_sum(cap, 20)

# 2. 计算市场收益率的Z-score
market_return_zscore = ts_zscore(weighted_returns, 20)

# 3. 计算波动率的Z-score
volatility_zscore = ts_zscore(ts_std_dev(returns, 20), 20)

# 4. 构建Mkt-Rf因子
mkt_rf_factor = market_return_zscore - volatility_zscore
```

## 使用步骤

### 1. 数据验证
确保以下字段在数据库中可用：
- `returns`: 日收益率
- `cap`: 市值（百万美元）
- `close`: 收盘价

### 2. 参数设置
- **时间窗口**: 20天（可根据需要调整）
- **市值加权**: 使用cap字段进行加权
- **标准化**: 使用Z-score进行标准化

### 3. 因子解释
- **正值**: 表示市场收益率高于风险调整水平
- **负值**: 表示市场收益率低于风险调整水平
- **绝对值大小**: 表示偏离程度

## 备选方案

### 方案A：基于排名
```fast
market_return_rank = rank(ts_mean(weighted_returns, 20))
volatility_rank = rank(ts_std_dev(returns, 20))
mkt_rf_factor_rank = market_return_rank - volatility_rank
```

### 方案B：多时间窗口
```fast
short_return = ts_mean(weighted_returns, 5)
medium_return = ts_mean(weighted_returns, 20)
long_return = ts_mean(weighted_returns, 60)
mkt_rf_factor_multi = (0.5 * short_return + 0.3 * medium_return + 0.2 * long_return)
```

### 方案C：动态权重
```fast
market_vol = ts_std_dev(returns, 20)
vol_weight = ts_scale(market_vol, 20)
mkt_rf_factor_dynamic = (1 - vol_weight) * market_return_zscore + vol_weight * volatility_zscore
```

## 参数调优建议

### 时间窗口选择
- **短期（5-10天）**: 捕捉短期市场情绪
- **中期（20-30天）**: 平衡稳定性和时效性
- **长期（60-120天）**: 反映长期趋势

### 权重调整
- **牛市环境**: 增加收益率权重
- **熊市环境**: 增加波动率权重
- **震荡市场**: 平衡权重

## 注意事项

1. **数据质量**: 确保returns和cap字段的完整性
2. **异常值处理**: Z-score有助于处理异常值
3. **市场环境**: 根据市场环境调整参数
4. **回测验证**: 建议进行充分的历史回测

## 扩展应用

### 与其他因子结合
```fast
# 结合价值因子
value_factor = rank(close / eps)
combined_factor = 0.7 * mkt_rf_factor + 0.3 * value_factor

# 结合动量因子
momentum_factor = ts_mean(returns, 20)
combined_factor = 0.5 * mkt_rf_factor + 0.5 * momentum_factor
```

### 行业中性化
```fast
# 如果需要行业中性化
# mkt_rf_factor_neutralized = group_neutralize(mkt_rf_factor, industry_group)
```

## 文件说明

- `美股Mkt-Rf因子构建方案.md`: 详细的技术方案文档
- `Mkt-Rf因子_FastExpression实现.fast`: 完整的fast expression实现
- `Mkt-Rf因子_简化版.fast`: 简化版实现，可直接使用
- `Mkt-Rf因子使用说明.md`: 本使用说明文档

## 技术支持

如需技术支持或参数调优建议，请参考：
1. 数据库字段说明
2. 操作符文档
3. Fast expression语法指南 