# 动态A矩阵调制实现

这是ProFoNet v2中状态调制聚焦Mamba（SMFM）的核心组件实现，专门用于实现状态转移矩阵A的动态调制功能。

## 🎯 核心功能

- **动态状态转移矩阵调制**：根据输入内容动态调制Mamba的状态转移矩阵A
- **数值稳定性保证**：内置多种数值稳定性检查和修正机制
- **完整的备份系统**：支持参数备份和恢复，确保训练安全性
- **灵活的选通机制**：支持多种格式的选通值输入
- **详细的监控统计**：提供丰富的调制效果统计信息

## 📁 文件结构

```
├── mamba_ssm/modules/dynamic_a_matrix.py  # 核心实现
├── test_dynamic_a_matrix.py               # 功能测试
├── example_usage.py                       # 使用示例
├── README_Dynamic_A_Matrix.md             # 说明文档
└── A_Matrix_Modulation_Implementation.md  # 详细技术文档
```

## 🚀 快速开始

### 1. 基本使用

```python
from mamba_ssm.modules.dynamic_a_matrix import DynamicAMatrixModulator, SimpleGatingNetwork

# 创建调制器
d_inner, d_state = 64, 16
modulator = DynamicAMatrixModulator(d_inner, d_state)

# 创建选通网络
gating_net = SimpleGatingNetwork(input_dim=256)

# 创建备份
modulator.create_backup()

# 使用
batch_size = 4
input_features = torch.randn(batch_size, 256)
gating_values = gating_net(input_features)  # (batch_size, 1)

# 获取动态调制的A矩阵
A_modulated = modulator.modulate_A_matrix(gating_values)  # (batch_size, d_inner, d_state)
```

### 2. 集成到现有模型

```python
class YourMambaModel(nn.Module):
    def __init__(self, d_model=256, d_state=16, use_dynamic_A=True):
        super().__init__()
        self.d_inner = d_model * 2
        
        if use_dynamic_A:
            self.A_modulator = DynamicAMatrixModulator(self.d_inner, d_state)
            self.gating_network = SimpleGatingNetwork(d_model)
            self.A_modulator.create_backup()  # 创建初始备份
        
    def forward(self, x):
        if hasattr(self, 'A_modulator'):
            # 计算选通值
            gating_values = self.gating_network(x.mean(dim=1))
            
            # 获取动态A矩阵
            A_modulated = self.A_modulator.modulate_A_matrix(gating_values)
            
            # 在这里使用A_modulated进行SSM计算
            # ...
```

### 3. 自动备份管理

```python
from mamba_ssm.modules.dynamic_a_matrix import AutoBackupManager

# 创建自动备份管理器
backup_manager = AutoBackupManager(modulator, backup_interval=100)

# 在训练循环中
for epoch in range(num_epochs):
    for batch in dataloader:
        # 训练代码...
        
        # 每100步自动创建备份
        backup_manager.step()
        
        # 如果训练出现问题，可以恢复
        try:
            loss.backward()
            optimizer.step()
        except:
            backup_manager.restore_checkpoint(-1)  # 恢复最新检查点
```

## 🧪 运行测试

### 功能测试
```bash
python test_dynamic_a_matrix.py
```

这将运行6个测试用例：
1. 基本功能测试
2. 梯度流测试
3. 数值稳定性测试
4. 备份系统测试
5. 自动备份管理器测试
6. 调制效果可视化

### 使用示例
```bash
python example_usage.py
```

这将演示：
- 如何创建带动态A矩阵的模型
- 训练过程中的使用方法
- 推理时的调制效果

## 📊 核心API

### DynamicAMatrixModulator

#### 主要方法

- `__init__(d_inner, d_state, device=None, dtype=None)`
  - 初始化调制器
  
- `modulate_A_matrix(gating_values) -> torch.Tensor`
  - 根据选通值动态调制A矩阵
  - 支持多种输入格式：(B,1), (B,d_inner), (B,seq_len)
  
- `get_base_A_matrix() -> torch.Tensor`
  - 获取基础A矩阵
  
- `create_backup() -> None`
  - 创建参数备份
  
- `restore_from_backup() -> bool`
  - 从备份恢复参数
  
- `get_modulation_stats(gating_values) -> Dict[str, float]`
  - 获取详细的调制统计信息

#### 统计信息说明

```python
stats = modulator.get_modulation_stats(gating_values)
# 返回字典包含：
# - base_A_mean/std/min/max: 基础A矩阵统计
# - modulated_A_mean/std: 调制后A矩阵统计  
# - gating_mean/std/min/max: 选通值统计
# - modulation_strength_mean/max: 调制强度
# - effective_gating_ratio: 有效选通比例(>0.5)
```

### SimpleGatingNetwork

轻量级选通网络，用于从输入特征生成选通值。

- `__init__(input_dim, hidden_dim=64, dropout=0.1)`
- `forward(x) -> torch.Tensor`
  - 输入：(batch_size, input_dim) 或 (batch_size, seq_len, input_dim)
  - 输出：(batch_size, 1) 或 (batch_size, seq_len, 1)

### AutoBackupManager

自动备份管理器，用于训练过程中的自动备份。

- `__init__(modulator, backup_interval=100)`
- `step() -> None` - 训练步骤计数，自动创建备份
- `create_checkpoint() -> None` - 手动创建检查点
- `restore_checkpoint(step=-1) -> bool` - 恢复指定检查点
- `list_checkpoints() -> None` - 列出所有检查点

## ⚙️ 配置选项

### 数值稳定性参数

```python
modulator = DynamicAMatrixModulator(d_inner, d_state)
modulator.eps = 1e-8  # 数值精度阈值
modulator.max_value_threshold = 1e6  # 最大值阈值
```

### 备份配置

```python
modulator.enable_backup(True)  # 启用/禁用备份功能

# 自动备份配置
backup_manager = AutoBackupManager(
    modulator, 
    backup_interval=100,  # 备份间隔
)
backup_manager.max_backups = 10  # 最大备份数量
```

## 🔧 高级用法

### 自定义选通网络

```python
class CustomGatingNetwork(nn.Module):
    def __init__(self, input_dim):
        super().__init__()
        # 自定义网络结构
        self.layers = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        return self.layers(x)

# 使用自定义选通网络
gating_net = CustomGatingNetwork(256)
```

### 多尺度选通

```python
# 为不同维度使用不同的选通值
gating_values = torch.rand(batch_size, d_inner)  # 每个维度独立
A_modulated = modulator.modulate_A_matrix(gating_values)
```

### 训练监控

```python
# 在训练循环中监控调制效果
for epoch in range(num_epochs):
    for batch in dataloader:
        # ... 训练代码 ...
        
        # 获取调制统计
        stats = modulator.get_modulation_stats(gating_values)
        
        # 记录到tensorboard或wandb
        writer.add_scalar('gating/mean', stats['gating_mean'], step)
        writer.add_scalar('modulation/strength', stats['modulation_strength_mean'], step)
```

## 🚨 注意事项

1. **数值稳定性**：动态调制可能导致数值不稳定，建议：
   - 使用梯度裁剪：`torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)`
   - 监控选通值范围，避免极端值
   - 定期创建备份

2. **内存使用**：动态A矩阵会增加内存使用，特别是大batch size时

3. **训练稳定性**：建议使用三阶段训练策略：
   - 阶段1：仅使用静态A矩阵预训练
   - 阶段2：逐步启用动态调制
   - 阶段3：完全动态调制微调

4. **备份策略**：
   - 训练开始前创建初始备份
   - 定期创建检查点
   - 出现数值问题时及时恢复

## 📈 性能考虑

- **计算开销**：动态调制增加约10-20%的计算时间
- **内存开销**：额外需要存储batch维度的A矩阵
- **优化建议**：
  - 使用较小的选通网络
  - 考虑使用混合精度训练
  - 在推理时可以禁用动态调制以提高速度

## 🤝 贡献

如果您发现bug或有改进建议，请：
1. 运行测试确认问题：`python test_dynamic_a_matrix.py`
2. 提供详细的错误信息和复现步骤
3. 如果可能，提供修复建议

## 📄 许可证

本实现遵循与原始Mamba项目相同的许可证。
