### **核心战略：首先聚焦第一阶段，成功后演进至第二阶段**

我们的战略路径清晰且务实：首先，我们将全部资源集中在第一阶段（iOS App）的开发上，打造一款以“所言即所得”为核心亮点的爆款产品。在市场验证成功后，再将核心体验平滑演进至第二阶段（Vision Pro），构筑产品的长期竞争壁垒。

---

### **第一阶段：iOS App - 奠定创意与社交基础**

此阶段的目标是快速推出一款最小可行产品（MVP），验证核心的AI生成玩法和社交概念，积累早期用户。其可行性极高，技术栈成熟，能快速交付核心价值。

*   **平台**：iOS (iPhone & iPad)
*   **技术栈**：
    *   **前端**：`SwiftUI` (为了未来与visionOS代码复用)。
    *   **本地数据**：`SwiftData` (用于持久化存储用户创作的香囊)。
    *   **后端**：`Firebase` (使用Firestore数据库、Storage存储图片、Authentication进行用户认证，快速搭建后端服务)。
*   **核心功能实现**：
    1.  **AI文字DIY2D香囊卡片**：这是“所言即所得”体验的核心。用户在`TextField`中输入描述，App调用`DALL-E 3`等成熟API，即时返回生成的2D香囊图片并展示。
    2.  **香囊收藏与祈福**：用户可将满意的作品和“咒语”(Prompt)保存到本地百宝箱。
    3.  **社交分享**：通过`ShareLink`将作品分享给好友，或发布到公共的“祈福墙”。

---

### **第二阶段：Vision Pro - 体验飞跃：情景化与动态交互 **

在第一阶段成功后，我们将启动Vision Pro应用的开发。其核心价值不再是简单展示3D模型，而是通过 **动态交互** 和 **AI情景化** 两大支柱，为用户带来真正的沉浸感。

*   **动态交互 (Dynamic Interaction)**：我们利用`RealityKit`的物理引擎，让香囊不再是一个静态模型。用户可以用手势“拿起”它，感受模拟的重量，摇晃时会触发粒子光效和音效，每一次互动都有即时、生动的反馈。
*   **AI情景化 (AI Contextualization)**：这是体验的升华。用户可以描述一个场景，如“将香囊挂在一棵月下的桂花树上”。我们通过大语言模型（如GPT-4）解析这段描述，从3D素材库中智能组合出对应的沉浸式环境，将一个孤立的物件变成一个完整、充满情感的故事场景。

**核心技术实现**：
*   **平台**：visionOS
*   **技术栈**：`RealityKit`, `ARKit`, `USDZ`
*   **AI 文字生成 3D 香囊**：调用`Luma AI Genie`等Text-to-3D API，将用户描述直接生成为3D模型。这是主要的技术风险点，我们将通过使用预设模型库进行并行开发来对冲风险。
*   **物理与手势交互**：通过`PhysicsBodyComponent`实现物理模拟，并利用visionOS原生的手部追踪实现精细的手势交互。
*   **AI 场景生成**：通过`LLM解析` + `3D素材库匹配` + `RealityKit场景布局`的方式，将文字描述转化为3D环境。

---

### 未来展望: 智能香薰硬件 - 虚实融合，五感闭环

此阶段是产品的终极形态，通过定制硬件将虚拟的嗅觉体验带入现实。



