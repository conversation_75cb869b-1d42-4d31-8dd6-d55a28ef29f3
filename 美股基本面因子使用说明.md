# 美股基本面因子使用说明

## 概述
本文档提供了使用MCP工具和fast expression语法构建美股基本面因子的完整方案，所有因子都基于简单明确的经济学逻辑构建。

## 数据库信息
- **数据源**: USA 1 TOP3000
- **数据字段**: close, act_12m_bps_value, act_12m_eps_value, anl44_roe_best_crncy_iso, cap, volume等
- **操作符**: ts_zscore, ts_mean, ts_delay, group_zscore, bucket, rank等

## 因子模板

### 1. 盈利能力定价因子 (`美股基本面因子_经济学理论版.fast`)
**理论基础**: 企业盈利能力与市场定价的关系
**构建逻辑**: ROE / P/E比率
**经济学含义**: 衡量盈利能力与定价的匹配度

### 2. 价值因子 (`美股价值因子_经济学理论版.fast`)
**理论基础**: 账面价值与市场价格的偏离度
**构建逻辑**: 1 / P/B比率
**经济学含义**: 衡量股票被低估的程度

### 3. 质量因子 (`美股质量因子_经济学理论版.fast`)
**理论基础**: 企业盈利能力
**构建逻辑**: ROE
**经济学含义**: 衡量企业盈利能力

### 4. 动量因子 (`美股动量因子_经济学理论版.fast`)
**理论基础**: 价格动量
**构建逻辑**: 过去N天的平均收益率
**经济学含义**: 衡量价格趋势强度

### 5. 反转因子优化版 (`美股反转因子_优化版.fast`) ⭐
**理论基础**: 价格反转效应
**构建逻辑**: 负动量 + 市值分组标准化
**经济学含义**: 捕捉价格反转机会
**特点**: 基于用户发现的有效信号因子优化

## 反转因子详细说明

### 原始有效因子
```fast
group_zscore(-ts_mean(close / ts_delay(close, 1) - 1, 20), bucket(rank(cap), range="0, 1, 0.1"))
```

### 优化版本

#### 方案1：稳定性优化（推荐）
```fast
# 基础反转信号
base_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 20)

# 稳定性调整
volatility = ts_std_dev(close / ts_delay(close, 1) - 1, 20)
stability_adjustment = 1 / (1 + volatility)
stable_reversal = base_reversal * stability_adjustment

# 市值分组标准化
final_factor = group_zscore(stable_reversal, bucket(rank(cap), range="0, 1, 0.1"))
```

#### 方案2：多时间窗口
```fast
# 短期反转（10天）
short_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 10)
# 中期反转（20天）
medium_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 20)
# 长期反转（40天）
long_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 40)

# 综合反转因子
combined_reversal = 0.4 * short_reversal + 0.4 * medium_reversal + 0.2 * long_reversal

# 市值分组标准化
final_factor = group_zscore(combined_reversal, bucket(rank(cap), range="0, 1, 0.1"))
```

#### 方案3：强度优化
```fast
# 基础反转信号
base_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 20)

# 反转强度（夏普比率思想）
reversal_sharpe = base_reversal / (ts_std_dev(close / ts_delay(close, 1) - 1, 20) + 0.001)

# 市值分组标准化
final_factor = group_zscore(reversal_sharpe, bucket(rank(cap), range="0, 1, 0.1"))
```

## 简单明确的方法

### 1. 单一逻辑
- **一个核心指标**: 每个因子基于一个核心指标
- **简单计算**: 避免复杂的组合和计算
- **易于理解**: 因子含义一目了然

### 2. 经济学含义明确
- **理论基础**: 每个因子都有明确的经济学理论基础
- **逻辑清晰**: 从理论到实践的简单逻辑
- **直接有效**: 避免过度复杂化

## 使用步骤

### 1. 数据验证
确保以下字段在数据库中可用：
- `close`: 收盘价
- `act_12m_bps_value`: 账面价值
- `act_12m_eps_value`: 每股收益
- `anl44_roe_best_crncy_iso`: ROE
- `cap`: 市值
- `volume`: 成交量

### 2. 参数设置
- **时间窗口**: 20-60天（根据因子特性调整）
- **标准化**: 使用Z-score进行标准化
- **市值分组**: 10组（range="0, 1, 0.1"）

### 3. 因子选择建议

#### 反转投资策略（推荐）
```fast
# 使用反转因子优化版
final_factor = reversal_factor
```

#### 价值投资策略
```fast
# 使用价值因子
final_factor = value_factor
```

#### 质量投资策略
```fast
# 使用质量因子
final_factor = quality_factor
```

#### 动量投资策略
```fast
# 使用动量因子
final_factor = momentum_factor
```

#### 综合投资策略
```fast
# 使用盈利能力定价因子
final_factor = composite_factor
```

## 经济学理论基础

### 1. 反转效应理论
- **反转效应**: 短期价格过度反应后的修正
- **市值效应**: 不同市值股票的反转特征差异
- **稳定性理论**: 稳定的反转信号更可靠

### 2. 盈利能力定价理论
- **企业价值理论**: 盈利能力决定企业价值
- **市场效率理论**: 市场定价存在偏差
- **价值投资理论**: 购买被低估的优质企业

### 3. 价值投资理论
- **价值投资理论**: 购买价格低于价值的股票
- **Fama-French价值因子**: 账面价值比率与收益的关系
- **市场效率理论**: 价格偏离价值的程度

### 4. 企业质量理论
- **企业质量理论**: 盈利能力反映企业质量
- **竞争优势理论**: 高ROE企业具有竞争优势
- **投资理论**: 高质量企业长期表现更好

### 5. 动量效应理论
- **动量效应理论**: 价格趋势具有持续性
- **行为金融学**: 投资者反应不足
- **技术分析理论**: 趋势跟随策略

## 参数调优建议

### 时间窗口选择
- **短期（10天）**: 捕捉短期反转机会
- **中期（20天）**: 平衡稳定性和时效性
- **长期（40天）**: 反映长期反转趋势

### 市值分组设置
- **10组分组**: range="0, 1, 0.1"（推荐）
- **5组分组**: range="0, 1, 0.2"（简化版）
- **20组分组**: range="0, 1, 0.05"（精细版）

### 因子应用策略
- **单一因子**: 专注特定投资风格
- **简单有效**: 避免过度复杂化
- **理论驱动**: 基于经济学理论

## 注意事项

1. **数据质量**: 确保财务指标的完整性和准确性
2. **理论理解**: 理解每个因子的经济学含义
3. **市场环境**: 不同因子在不同市场环境下表现差异较大
4. **回测验证**: 建议进行充分的历史回测
5. **交易成本**: 考虑因子策略的交易成本

## 扩展应用

### 行业中性化
```fast
# 行业中性化
neutralized_factor = group_neutralize(final_factor, industry)
```

### 风险调整
```fast
# 波动率调整
volatility = ts_std_dev(close / ts_delay(close, 1) - 1, 20)
risk_adjusted_factor = final_factor / (1 + ts_zscore(volatility, 20))
```

### 简单组合
```fast
# 简单等权重组合
combined_factor = (value_factor + quality_factor + momentum_factor) / 3
```

## 文件说明

- `美股基本面因子_经济学理论版.fast`: 盈利能力定价因子
- `美股价值因子_经济学理论版.fast`: 价值因子
- `美股质量因子_经济学理论版.fast`: 质量因子
- `美股动量因子_经济学理论版.fast`: 动量因子
- `美股反转因子_优化版.fast`: 反转因子优化版 ⭐
- `美股动量因子_优化版.fast`: 动量因子优化版
- `美股基本面因子使用说明.md`: 本使用说明文档

## 技术支持

如需技术支持或参数调优建议，请参考：
1. 数据库字段说明
2. 操作符文档
3. Fast expression语法指南
4. 经济学理论文献 