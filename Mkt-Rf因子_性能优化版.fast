# Mkt-Rf因子性能优化版
# 专门解决波动较大问题

# ========================================
# 核心优化策略
# ========================================

# 1. 原始因子（问题版本）
market_volatility = ts_std_dev(returns, 20);
weighted_returns = returns * cap / ts_sum(cap, 20);
market_return = ts_mean(weighted_returns, 20);
original_mkt_rf = -market_return * market_volatility;

# ========================================
# 优化方案A：信号平滑（推荐）
# ========================================
# 使用winsorize处理异常值
smoothed_returns = winsorize(weighted_returns, std=3);
smoothed_volatility = winsorize(market_volatility, std=3);

# 多时间窗口平滑
short_smooth = ts_mean(smoothed_returns, 5);
medium_smooth = ts_mean(smoothed_returns, 20);
long_smooth = ts_mean(smoothed_returns, 60);

# 加权平滑信号
smoothed_market_return = 0.2 * short_smooth + 0.5 * medium_smooth + 0.3 * long_smooth;
smoothed_market_vol = ts_mean(smoothed_volatility, 20);

# 优化后的Mkt-Rf因子
optimized_mkt_rf_a = -smoothed_market_return * smoothed_market_vol;

# ========================================
# 优化方案B：波动率调整
# ========================================
# 计算波动率Z-score
volatility_zscore = ts_zscore(market_volatility, 20);

# 波动率调整的收益率
vol_adjusted_return = market_return * (1 - 0.5 * volatility_zscore);

# 优化后的Mkt-Rf因子
optimized_mkt_rf_b = -vol_adjusted_return * market_volatility;

# ========================================
# 优化方案C：市值分组优化
# ========================================
# 创建市值分组
market_cap_buckets = bucket(rank(cap), range="0, 1, 0.2");

# 分组内计算
group_return = group_mean(market_return, cap, market_cap_buckets);
group_vol = group_mean(market_volatility, cap, market_cap_buckets);

# 优化后的Mkt-Rf因子
optimized_mkt_rf_c = -group_return * group_vol;

# ========================================
# 优化方案D：信号过滤
# ========================================
# 计算信号强度
signal_strength = abs(market_return) / market_volatility;

# 过滤弱信号
signal_threshold = ts_mean(signal_strength, 20);
filtered_return = if_else(signal_strength > signal_threshold, 
                         market_return, 
                         0.5 * market_return);

# 优化后的Mkt-Rf因子
optimized_mkt_rf_d = -filtered_return * market_volatility;

# ========================================
# 最终推荐方案
# ========================================
# 组合优化方案
final_optimized_mkt_rf = (0.4 * optimized_mkt_rf_a + 
                          0.3 * optimized_mkt_rf_b + 
                          0.2 * optimized_mkt_rf_c + 
                          0.1 * optimized_mkt_rf_d);

# 最终标准化
final_mkt_rf_normalized = ts_zscore(final_optimized_mkt_rf, 20);

# ========================================
# 备选简化方案
# ========================================
# 最简单的优化：只使用winsorize
simple_smoothed_return = winsorize(market_return, std=3);
simple_smoothed_vol = winsorize(market_volatility, std=3);
simple_optimized_mkt_rf = -simple_smoothed_return * simple_smoothed_vol;

# ========================================
# 输出最终因子
# ========================================
# 主要推荐
mkt_rf_factor = final_mkt_rf_normalized;

# 备选方案
mkt_rf_factor_alternative = simple_optimized_mkt_rf; 