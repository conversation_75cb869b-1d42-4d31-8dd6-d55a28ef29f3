# 美股动量因子 - 优化版
# 基于用户发现的有效信号因子

## 原始因子分析
# 用户发现的有效因子：
# group_zscore(-ts_mean(close / ts_delay(close, 1) - 1, 20), bucket(rank(cap), range="0, 1, 0.1"))
# 
# 因子含义：
# 1. ts_mean(close / ts_delay(close, 1) - 1, 20): 20天平均收益率
# 2. -ts_mean(...): 反转信号（负动量）
# 3. rank(cap): 市值排名
# 4. bucket(rank(cap), range="0, 1, 0.1"): 按市值分10组
# 5. group_zscore(...): 在市值分组内进行Z-score标准化

## 优化版本

### 方案1：多时间窗口动量
```fast
# 短期动量（5天）
short_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 60)

# 综合动量因子
momentum_factor = (0.5 * short_momentum + 0.3 * medium_momentum + 0.2 * long_momentum)

# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案2：动量强度优化
```fast
# 基础动量
base_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 20)

# 动量强度（使用标准差调整）
momentum_volatility = ts_std_dev(close / ts_delay(close, 1) - 1, 20)
momentum_intensity = base_momentum / (momentum_volatility + 0.001)

# 市值分组标准化
final_factor = group_zscore(momentum_intensity, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案3：成交量确认动量
```fast
# 基础动量
base_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 20)

# 成交量确认
volume_ratio = volume / ts_mean(volume, 20)
volume_confirmation = ts_corr(close / ts_delay(close, 1) - 1, volume_ratio, 20)

# 动量与成交量确认结合
momentum_with_volume = base_momentum * (1 + abs(volume_confirmation))

# 市值分组标准化
final_factor = group_zscore(momentum_with_volume, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案4：推荐优化版本
```fast
# 基础反转动量
base_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 20)

# 动量稳定性调整
momentum_stability = 1 / (1 + ts_std_dev(close / ts_delay(close, 1) - 1, 20))
adjusted_momentum = base_momentum * momentum_stability

# 市值分组标准化
final_factor = group_zscore(adjusted_momentum, bucket(rank(cap), range="0, 1, 0.1"))
```

## 参数说明
# 时间窗口：5天（短期）、20天（中期）、60天（长期）
# 市值分组：10组（range="0, 1, 0.1"）
# 权重分配：短期50%、中期30%、长期20%

## 经济学理论支持
# 1. 反转效应：短期价格反转在市场中普遍存在
# 2. 市值效应：不同市值股票的反转特征不同
# 3. 动量稳定性：稳定的动量信号更可靠

## 因子特性
# - 反转信号：负动量捕捉价格反转机会
# - 市值分组：控制市值效应的影响
# - 多时间窗口：捕捉不同时间尺度的反转
# - 稳定性调整：提高信号的可靠性

## 使用建议
# 1. 方案1适合捕捉多时间尺度的反转机会
# 2. 方案2适合在波动率较高的市场环境
# 3. 方案3适合需要成交量确认的交易策略
# 4. 方案4是推荐的平衡版本，兼顾稳定性和有效性 