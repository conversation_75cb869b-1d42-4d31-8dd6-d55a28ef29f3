# 美股反转因子 - 优化版
# 基于用户发现的有效信号因子优化

## 原始有效因子
# group_zscore(-ts_mean(close / ts_delay(close, 1) - 1, 20), bucket(rank(cap), range="0, 1, 0.1"))

## 优化版本

### 方案1：稳定性优化（推荐）
```fast
# 基础反转信号
base_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 20)

# 稳定性调整
volatility = ts_std_dev(close / ts_delay(close, 1) - 1, 20)
stability_adjustment = 1 / (1 + volatility)
stable_reversal = base_reversal * stability_adjustment

# 市值分组标准化
final_factor = group_zscore(stable_reversal, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案2：多时间窗口
```fast
# 短期反转（10天）
short_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 10)
# 中期反转（20天）
medium_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 20)
# 长期反转（40天）
long_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 40)

# 综合反转因子
combined_reversal = 0.4 * short_reversal + 0.4 * medium_reversal + 0.2 * long_reversal

# 市值分组标准化
final_factor = group_zscore(combined_reversal, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案3：强度优化
```fast
# 基础反转信号
base_reversal = -ts_mean(close / ts_delay(close, 1) - 1, 20)

# 反转强度（夏普比率思想）
reversal_sharpe = base_reversal / (ts_std_dev(close / ts_delay(close, 1) - 1, 20) + 0.001)

# 市值分组标准化
final_factor = group_zscore(reversal_sharpe, bucket(rank(cap), range="0, 1, 0.1"))
```

## 经济学理论支持
# 1. 反转效应：短期价格过度反应后的修正
# 2. 市值效应：不同市值股票的反转特征差异
# 3. 稳定性理论：稳定的反转信号更可靠

## 因子特性
# - 反转信号：捕捉价格反转机会
# - 市值中性：控制市值效应影响
# - 稳定性：提高信号可靠性
# - 多时间尺度：捕捉不同反转周期

## 使用建议
# 方案1：适合大多数市场环境，平衡稳定性和有效性
# 方案2：适合捕捉不同时间尺度的反转机会
# 方案3：适合风险调整后的反转策略 