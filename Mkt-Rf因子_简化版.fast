# 美股Mkt-Rf因子 - 简化版实现
# 适用于 USA 1 TOP3000 数据
# 使用数据库支持的组件和fast expression语法

# ========================================
# 核心Mkt-Rf因子实现
# ========================================

# 1. 计算市值加权收益率
weighted_returns = returns * cap / ts_sum(cap, 20)

# 2. 计算市场收益率的Z-score（标准化）
market_return_zscore = ts_zscore(weighted_returns, 20)

# 3. 计算波动率的Z-score（风险代理）
volatility_zscore = ts_zscore(ts_std_dev(returns, 20), 20)

# 4. 构建Mkt-Rf因子（市场收益率 - 风险调整）
mkt_rf_factor = market_return_zscore - volatility_zscore

# ========================================
# 备选实现方案
# ========================================

# 方案A：基于排名的Mkt-Rf因子
market_return_rank = rank(ts_mean(weighted_returns, 20))
volatility_rank = rank(ts_std_dev(returns, 20))
mkt_rf_factor_rank = market_return_rank - volatility_rank

# 方案B：多时间窗口Mkt-Rf因子
short_return = ts_mean(weighted_returns, 5)
medium_return = ts_mean(weighted_returns, 20)
long_return = ts_mean(weighted_returns, 60)
mkt_rf_factor_multi = (0.5 * short_return + 0.3 * medium_return + 0.2 * long_return)

# 方案C：动态权重Mkt-Rf因子
market_vol = ts_std_dev(returns, 20)
vol_weight = ts_scale(market_vol, 20)
mkt_rf_factor_dynamic = (1 - vol_weight) * market_return_zscore + vol_weight * volatility_zscore

# ========================================
# 最终推荐因子
# ========================================
# 主要因子：Z-score标准化Mkt-Rf因子
final_mkt_rf_factor = mkt_rf_factor 