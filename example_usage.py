"""
动态A矩阵调制的使用示例

展示如何在实际项目中使用动态A矩阵调制功能
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import sys
import os

# 添加路径
sys.path.append('.')

from mamba_ssm.modules.dynamic_a_matrix import (
    DynamicAMatrixModulator, 
    SimpleGatingNetwork, 
    AutoBackupManager
)


class MambaWithDynamicA(nn.Module):
    """
    集成动态A矩阵的简化Mamba模型
    
    这是一个示例实现，展示如何将动态A矩阵调制集成到现有模型中
    """
    
    def __init__(self, d_model=256, d_state=16, d_inner=None, use_dynamic_A=True):
        super().__init__()
        
        self.d_model = d_model
        self.d_state = d_state
        self.d_inner = d_inner or int(2 * d_model)  # 扩展因子为2
        self.use_dynamic_A = use_dynamic_A
        
        # 输入投影
        self.in_proj = nn.Linear(d_model, self.d_inner * 2)  # x和z
        
        # 动态A矩阵调制器
        if use_dynamic_A:
            self.A_modulator = DynamicAMatrixModulator(self.d_inner, d_state)
            self.gating_network = SimpleGatingNetwork(d_model, hidden_dim=64)
            # 创建初始备份
            self.A_modulator.create_backup()
            print(f"[MambaWithDynamicA] 启用动态A矩阵调制")
        else:
            # 传统静态A矩阵
            A = torch.arange(1, d_state + 1, dtype=torch.float32)
            A = A.repeat(self.d_inner, 1)
            A_log = torch.log(A)
            self.A_log = nn.Parameter(A_log)
            self.A_log._no_weight_decay = True
            print(f"[MambaWithDynamicA] 使用静态A矩阵")
        
        # 其他必要的参数（简化版本）
        self.D = nn.Parameter(torch.ones(self.d_inner))
        self.out_proj = nn.Linear(self.d_inner, d_model)
        
        # 激活函数
        self.activation = nn.SiLU()
        
    def forward(self, x, return_gating_info=False):
        """
        前向传播
        
        Args:
            x: 输入张量 (batch_size, seq_len, d_model)
            return_gating_info: 是否返回选通信息
            
        Returns:
            output: 输出张量 (batch_size, seq_len, d_model)
            gating_info: 选通信息（可选）
        """
        batch_size, seq_len, d_model = x.shape
        
        # 输入投影
        x_proj = self.in_proj(x)  # (batch, seq_len, d_inner * 2)
        x_ssm, z = x_proj.chunk(2, dim=-1)  # 各自 (batch, seq_len, d_inner)
        
        gating_info = {}
        
        if self.use_dynamic_A and self.training:
            # 动态A矩阵模式
            
            # 计算选通值（使用序列的平均特征）
            x_mean = x.mean(dim=1)  # (batch, d_model)
            gating_values = self.gating_network(x_mean)  # (batch, 1)
            
            # 获取动态调制的A矩阵
            A_modulated = self.A_modulator.modulate_A_matrix(gating_values)  # (batch, d_inner, d_state)
            
            # 存储选通信息
            if return_gating_info:
                gating_info = {
                    'gating_values': gating_values,
                    'A_modulated': A_modulated,
                    'modulation_stats': self.A_modulator.get_modulation_stats(gating_values)
                }
            
            # 简化的SSM计算（这里只是示例，实际需要完整的selective scan）
            # 在实际实现中，这里应该调用selective_scan_fn
            y = self._simplified_ssm_forward(x_ssm, A_modulated)
            
        else:
            # 静态A矩阵模式
            if hasattr(self, 'A_log'):
                A_static = -torch.exp(self.A_log.float())
            else:
                A_static = self.A_modulator.get_base_A_matrix()
            
            # 扩展到batch维度
            A_expanded = A_static.unsqueeze(0).expand(batch_size, -1, -1)
            
            # 简化的SSM计算
            y = self._simplified_ssm_forward(x_ssm, A_expanded)
        
        # 门控和输出投影
        y = y * self.activation(z)
        output = self.out_proj(y)
        
        if return_gating_info:
            return output, gating_info
        return output
    
    def _simplified_ssm_forward(self, x, A):
        """
        简化的SSM前向传播（仅用于演示）
        
        在实际实现中，这里应该使用完整的selective scan算法
        """
        # 这是一个极简化的版本，仅用于演示
        # 实际应该使用 selective_scan_fn
        
        batch_size, seq_len, d_inner = x.shape
        
        # 简单的线性变换作为占位符
        # 在真实实现中，这里需要完整的状态空间模型计算
        y = x * 0.5 + torch.randn_like(x) * 0.1  # 占位符计算
        
        return y
    
    def get_A_matrix_info(self):
        """获取A矩阵相关信息"""
        if self.use_dynamic_A:
            base_A = self.A_modulator.get_base_A_matrix()
            return {
                'type': 'dynamic',
                'base_A_shape': base_A.shape,
                'base_A_mean': base_A.mean().item(),
                'base_A_std': base_A.std().item(),
                'has_backup': self.A_modulator._backup_A_log is not None
            }
        else:
            A_static = -torch.exp(self.A_log.float())
            return {
                'type': 'static',
                'A_shape': A_static.shape,
                'A_mean': A_static.mean().item(),
                'A_std': A_static.std().item()
            }


def create_dummy_dataset(num_samples=1000, seq_len=64, d_model=256):
    """创建虚拟数据集用于演示"""
    # 生成随机输入数据
    X = torch.randn(num_samples, seq_len, d_model)
    
    # 生成随机标签（二分类）
    y = torch.randint(0, 2, (num_samples,))
    
    return TensorDataset(X, y)


def train_example():
    """训练示例"""
    print("=" * 60)
    print("动态A矩阵调制训练示例")
    print("=" * 60)
    
    # 模型参数
    d_model = 128
    d_state = 16
    batch_size = 8
    seq_len = 32
    num_epochs = 3
    
    # 创建模型
    model_dynamic = MambaWithDynamicA(d_model=d_model, d_state=d_state, use_dynamic_A=True)
    model_static = MambaWithDynamicA(d_model=d_model, d_state=d_state, use_dynamic_A=False)
    
    # 创建数据
    dataset = create_dummy_dataset(num_samples=100, seq_len=seq_len, d_model=d_model)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    
    # 优化器
    optimizer_dynamic = optim.Adam(model_dynamic.parameters(), lr=1e-4)
    optimizer_static = optim.Adam(model_static.parameters(), lr=1e-4)
    
    # 损失函数
    criterion = nn.CrossEntropyLoss()
    
    # 自动备份管理器（仅用于动态模型）
    backup_manager = AutoBackupManager(model_dynamic.A_modulator, backup_interval=5)
    
    print(f"动态模型A矩阵信息: {model_dynamic.get_A_matrix_info()}")
    print(f"静态模型A矩阵信息: {model_static.get_A_matrix_info()}")
    
    # 训练循环
    for epoch in range(num_epochs):
        print(f"\nEpoch {epoch + 1}/{num_epochs}")
        print("-" * 30)
        
        epoch_loss_dynamic = 0
        epoch_loss_static = 0
        num_batches = 0
        
        for batch_idx, (data, targets) in enumerate(dataloader):
            # 训练动态模型
            model_dynamic.train()
            optimizer_dynamic.zero_grad()
            
            output_dynamic, gating_info = model_dynamic(data, return_gating_info=True)
            
            # 简单的分类头（取序列平均）
            logits_dynamic = output_dynamic.mean(dim=1)  # (batch, d_model)
            logits_dynamic = torch.randn(batch_size, 2)  # 占位符，实际需要分类头
            
            loss_dynamic = criterion(logits_dynamic, targets)
            loss_dynamic.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model_dynamic.parameters(), max_norm=1.0)
            
            optimizer_dynamic.step()
            backup_manager.step()
            
            # 训练静态模型
            model_static.train()
            optimizer_static.zero_grad()
            
            output_static = model_static(data)
            logits_static = torch.randn(batch_size, 2)  # 占位符
            
            loss_static = criterion(logits_static, targets)
            loss_static.backward()
            
            torch.nn.utils.clip_grad_norm_(model_static.parameters(), max_norm=1.0)
            optimizer_static.step()
            
            epoch_loss_dynamic += loss_dynamic.item()
            epoch_loss_static += loss_static.item()
            num_batches += 1
            
            # 打印选通信息
            if batch_idx == 0:  # 只打印第一个batch的信息
                stats = gating_info['modulation_stats']
                print(f"  选通值统计: mean={stats['gating_mean']:.3f}, "
                      f"std={stats['gating_std']:.3f}")
                print(f"  调制强度: {stats['modulation_strength_mean']:.6f}")
                print(f"  有效选通比例: {stats['effective_gating_ratio']:.3f}")
        
        avg_loss_dynamic = epoch_loss_dynamic / num_batches
        avg_loss_static = epoch_loss_static / num_batches
        
        print(f"  动态模型平均损失: {avg_loss_dynamic:.6f}")
        print(f"  静态模型平均损失: {avg_loss_static:.6f}")
    
    print("\n训练完成!")
    
    # 最终统计
    print("\n最终模型信息:")
    print(f"动态模型: {model_dynamic.get_A_matrix_info()}")
    print(f"静态模型: {model_static.get_A_matrix_info()}")


def inference_example():
    """推理示例"""
    print("\n" + "=" * 60)
    print("推理示例")
    print("=" * 60)
    
    # 创建模型
    model = MambaWithDynamicA(d_model=128, d_state=16, use_dynamic_A=True)
    model.eval()
    
    # 创建测试数据
    test_data = torch.randn(1, 32, 128)  # (batch=1, seq_len=32, d_model=128)
    
    print(f"输入数据形状: {test_data.shape}")
    
    with torch.no_grad():
        # 推理
        output, gating_info = model(test_data, return_gating_info=True)
        
        print(f"输出数据形状: {output.shape}")
        print(f"选通值: {gating_info['gating_values'].item():.6f}")
        
        # 打印调制统计
        stats = gating_info['modulation_stats']
        print("\n调制统计信息:")
        for key, value in stats.items():
            print(f"  {key}: {value:.6f}")
    
    print("推理完成!")


if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    
    try:
        # 运行训练示例
        train_example()
        
        # 运行推理示例
        inference_example()
        
        print("\n🎉 所有示例运行成功!")
        
    except Exception as e:
        print(f"\n❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()
