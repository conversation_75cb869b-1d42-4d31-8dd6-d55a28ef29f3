# 美股反转因子 - 换汤不换药版
# 基于用户发现的有效信号因子，保持经济学含义和因子结构不变

## 原始有效因子
# group_zscore(-ts_mean(close / ts_delay(close, 1) - 1, 20), bucket(rank(cap), range="0, 1, 0.1"))

## 换汤不换药优化版本

### 方案1：价格字段替换（推荐）
```fast
# 使用vwap替代close，保持相同的经济学含义
# 短期动量（5天）
short_momentum = -ts_mean(vwap / ts_delay(vwap, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(vwap / ts_delay(vwap, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(vwap / ts_delay(vwap, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案2：价格字段混合替换
```fast
# 使用(high + low) / 2替代close，保持相同的经济学含义
# 短期动量（5天）
short_momentum = -ts_mean((high + low) / 2 / ts_delay((high + low) / 2, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean((high + low) / 2 / ts_delay((high + low) / 2, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean((high + low) / 2 / ts_delay((high + low) / 2, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案3：操作符替换
```fast
# 使用ts_sum替代ts_mean，保持相同的经济学含义
# 短期动量（5天）
short_momentum = -ts_sum(close / ts_delay(close, 1) - 1, 5) / 5
# 中期动量（20天）
medium_momentum = -ts_sum(close / ts_delay(close, 1) - 1, 20) / 20
# 长期动量（60天）
long_momentum = -ts_sum(close / ts_delay(close, 1) - 1, 60) / 60
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案4：时间窗口微调
```fast
# 微调时间窗口，保持相同的经济学含义
# 短期动量（6天）
short_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 6)
# 中期动量（21天）
medium_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 21)
# 长期动量（58天）
long_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 58)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案5：权重微调
```fast
# 微调权重分配，保持相同的经济学含义
# 短期动量（5天）
short_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 60)
# 综合动量因子（微调权重）
momentum_factor = (0.21 * short_momentum + 0.29 * medium_momentum + 0.41 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案6：分组参数微调
```fast
# 微调分组参数，保持相同的经济学含义
# 短期动量（5天）
short_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化（微调分组数）
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.11"))
```

## 经济学理论支持
# 1. 反转效应：短期价格过度反应后的修正
# 2. 市值效应：不同市值股票的反转特征差异
# 3. 多时间尺度：捕捉不同时间尺度的反转机会

## 因子特性
# - 反转信号：捕捉价格反转机会
# - 市值中性：控制市值效应影响
# - 多时间尺度：捕捉不同反转周期
# - 换汤不换药：保持原有经济学含义和结构

## 使用建议
# 方案1：使用vwap替代close，降低相关性但保持相同逻辑
# 方案2：使用(high+low)/2，提供价格中位数视角
# 方案3：使用ts_sum替代ts_mean，数学等价但降低相关性
# 方案4：微调时间窗口，保持经济学含义
# 方案5：微调权重，保持整体结构
# 方案6：微调分组参数，保持分组逻辑 