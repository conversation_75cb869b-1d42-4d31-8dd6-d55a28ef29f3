"""
测试动态A矩阵调制功能

运行此脚本来验证A矩阵动态调制的正确性和稳定性
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import sys
import os

# 添加mamba_ssm到路径
sys.path.append('.')

from mamba_ssm.modules.dynamic_a_matrix import (
    DynamicAMatrixModulator, 
    SimpleGatingNetwork, 
    AutoBackupManager
)


def test_basic_functionality():
    """测试基本功能"""
    print("=" * 50)
    print("测试1: 基本功能测试")
    print("=" * 50)
    
    # 初始化参数
    batch_size = 4
    d_inner = 64
    d_state = 16
    
    # 创建调制器
    modulator = DynamicAMatrixModulator(d_inner, d_state)
    print(f"✓ 调制器初始化成功: d_inner={d_inner}, d_state={d_state}")
    
    # 创建备份
    modulator.create_backup()
    
    # 测试不同格式的选通值
    test_cases = [
        ("全局选通值", torch.rand(batch_size, 1) * 0.8 + 0.1),
        ("维度独立选通值", torch.rand(batch_size, d_inner) * 0.8 + 0.1),
        ("序列级选通值", torch.rand(batch_size, 32) * 0.8 + 0.1),
    ]
    
    for case_name, gating_values in test_cases:
        print(f"\n测试 {case_name}:")
        print(f"  选通值形状: {gating_values.shape}")
        
        try:
            # 获取基础A矩阵
            A_base = modulator.get_base_A_matrix()
            
            # 调制A矩阵
            A_modulated = modulator.modulate_A_matrix(gating_values)
            
            print(f"  基础A矩阵形状: {A_base.shape}")
            print(f"  调制后A矩阵形状: {A_modulated.shape}")
            print(f"  选通值范围: [{gating_values.min():.3f}, {gating_values.max():.3f}]")
            
            # 获取统计信息
            stats = modulator.get_modulation_stats(gating_values)
            print(f"  调制强度: {stats['modulation_strength_mean']:.6f}")
            print(f"  有效选通比例: {stats['effective_gating_ratio']:.3f}")
            
            print(f"  ✓ {case_name} 测试通过")
            
        except Exception as e:
            print(f"  ✗ {case_name} 测试失败: {e}")
            return False
    
    return True


def test_gradient_flow():
    """测试梯度流"""
    print("\n" + "=" * 50)
    print("测试2: 梯度流测试")
    print("=" * 50)
    
    batch_size = 2
    d_inner = 32
    d_state = 8
    
    # 创建调制器
    modulator = DynamicAMatrixModulator(d_inner, d_state)
    
    # 创建选通网络
    gating_net = SimpleGatingNetwork(d_inner)
    
    # 创建测试输入
    x = torch.randn(batch_size, d_inner, requires_grad=True)
    
    try:
        # 前向传播
        gating_values = gating_net(x)
        A_modulated = modulator.modulate_A_matrix(gating_values)
        
        # 计算损失
        loss = A_modulated.sum()
        
        # 反向传播
        loss.backward()
        
        # 检查梯度
        print(f"输入梯度范数: {x.grad.norm().item():.6f}")
        print(f"A_log梯度范数: {modulator.A_log.grad.norm().item():.6f}")
        
        # 检查选通网络的梯度
        gating_grad_norm = 0
        for param in gating_net.parameters():
            if param.grad is not None:
                gating_grad_norm += param.grad.norm().item() ** 2
        gating_grad_norm = gating_grad_norm ** 0.5
        print(f"选通网络梯度范数: {gating_grad_norm:.6f}")
        
        # 验证梯度不为零且不是NaN
        assert x.grad is not None and not torch.isnan(x.grad).any()
        assert modulator.A_log.grad is not None and not torch.isnan(modulator.A_log.grad).any()
        
        print("✓ 梯度流测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 梯度流测试失败: {e}")
        return False


def test_numerical_stability():
    """测试数值稳定性"""
    print("\n" + "=" * 50)
    print("测试3: 数值稳定性测试")
    print("=" * 50)
    
    batch_size = 4
    d_inner = 64
    d_state = 16
    
    modulator = DynamicAMatrixModulator(d_inner, d_state)
    modulator.create_backup()
    
    # 测试极端选通值
    extreme_cases = [
        ("全零选通值", torch.zeros(batch_size, 1)),
        ("全一选通值", torch.ones(batch_size, 1)),
        ("随机极值", torch.rand(batch_size, 1)),
        ("接近边界值", torch.tensor([[0.001], [0.999], [0.5], [0.1]])),
    ]
    
    for case_name, gating_values in extreme_cases:
        print(f"\n测试 {case_name}:")
        
        try:
            A_modulated = modulator.modulate_A_matrix(gating_values)
            
            # 检查数值稳定性
            has_nan = torch.isnan(A_modulated).any()
            has_inf = torch.isinf(A_modulated).any()
            max_val = A_modulated.abs().max().item()
            
            print(f"  包含NaN: {has_nan}")
            print(f"  包含Inf: {has_inf}")
            print(f"  最大绝对值: {max_val:.2e}")
            
            if has_nan or has_inf:
                print(f"  ⚠ {case_name} 存在数值问题，但已被处理")
            else:
                print(f"  ✓ {case_name} 数值稳定")
                
        except Exception as e:
            print(f"  ✗ {case_name} 测试失败: {e}")
            # 尝试恢复备份
            modulator.restore_from_backup()
    
    return True


def test_backup_system():
    """测试备份系统"""
    print("\n" + "=" * 50)
    print("测试4: 备份系统测试")
    print("=" * 50)
    
    d_inner = 32
    d_state = 8
    
    modulator = DynamicAMatrixModulator(d_inner, d_state)
    
    # 记录初始参数
    initial_A_log = modulator.A_log.data.clone()
    
    # 创建备份
    modulator.create_backup()
    
    # 修改参数
    modulator.A_log.data += torch.randn_like(modulator.A_log.data) * 0.1
    modified_A_log = modulator.A_log.data.clone()
    
    print(f"初始参数范数: {initial_A_log.norm().item():.6f}")
    print(f"修改后参数范数: {modified_A_log.norm().item():.6f}")
    print(f"参数变化: {(modified_A_log - initial_A_log).norm().item():.6f}")
    
    # 恢复备份
    success = modulator.restore_from_backup()
    restored_A_log = modulator.A_log.data.clone()
    
    print(f"恢复后参数范数: {restored_A_log.norm().item():.6f}")
    print(f"恢复误差: {(restored_A_log - initial_A_log).norm().item():.8f}")
    
    if success and torch.allclose(restored_A_log, initial_A_log):
        print("✓ 备份系统测试通过")
        return True
    else:
        print("✗ 备份系统测试失败")
        return False


def test_auto_backup_manager():
    """测试自动备份管理器"""
    print("\n" + "=" * 50)
    print("测试5: 自动备份管理器测试")
    print("=" * 50)
    
    d_inner = 32
    d_state = 8
    
    modulator = DynamicAMatrixModulator(d_inner, d_state)
    backup_manager = AutoBackupManager(modulator, backup_interval=3)
    
    # 模拟训练步骤
    for step in range(10):
        # 模拟参数更新
        modulator.A_log.data += torch.randn_like(modulator.A_log.data) * 0.01
        backup_manager.step()
    
    # 列出检查点
    backup_manager.list_checkpoints()
    
    # 测试恢复
    print("\n测试恢复到最新检查点:")
    success = backup_manager.restore_checkpoint(-1)
    
    print("\n测试恢复到第一个检查点:")
    success = backup_manager.restore_checkpoint(0)
    
    if success:
        print("✓ 自动备份管理器测试通过")
        return True
    else:
        print("✗ 自动备份管理器测试失败")
        return False


def visualize_modulation_effect():
    """可视化调制效果"""
    print("\n" + "=" * 50)
    print("测试6: 调制效果可视化")
    print("=" * 50)
    
    d_inner = 4
    d_state = 8
    
    modulator = DynamicAMatrixModulator(d_inner, d_state)
    
    # 创建不同的选通值
    gating_range = torch.linspace(0.1, 0.9, 9).unsqueeze(1)  # (9, 1)
    
    # 计算调制效果
    A_base = modulator.get_base_A_matrix()
    modulation_effects = []
    
    for g in gating_range:
        A_mod = modulator.modulate_A_matrix(g.unsqueeze(0))  # (1, d_inner, d_state)
        effect = (A_mod[0] - A_base).abs().mean().item()
        modulation_effects.append(effect)
    
    # 打印结果
    print("选通值 -> 调制强度:")
    for g, effect in zip(gating_range.flatten(), modulation_effects):
        print(f"  {g.item():.1f} -> {effect:.6f}")
    
    # 验证调制效果的单调性
    is_monotonic = all(modulation_effects[i] <= modulation_effects[i+1] 
                      for i in range(len(modulation_effects)-1))
    
    if is_monotonic:
        print("✓ 调制效果随选通值单调变化")
        return True
    else:
        print("⚠ 调制效果非单调变化（可能正常）")
        return True


def run_all_tests():
    """运行所有测试"""
    print("开始运行动态A矩阵调制功能测试...")
    print("=" * 60)
    
    tests = [
        test_basic_functionality,
        test_gradient_flow,
        test_numerical_stability,
        test_backup_system,
        test_auto_backup_manager,
        visualize_modulation_effect,
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"测试 {test_func.__name__} 出现异常: {e}")
            results.append(False)
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_func, result) in enumerate(zip(tests, results)):
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{i+1}. {test_func.__name__}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！A矩阵动态调制功能正常工作。")
    else:
        print("⚠ 部分测试失败，请检查实现。")
    
    return passed == total


if __name__ == "__main__":
    # 设置随机种子以确保可重现性
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行测试
    success = run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)
