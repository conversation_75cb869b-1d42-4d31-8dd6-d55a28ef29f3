import pandas as pd

def generate_monte_carlo_excel(filename="蒙特卡洛模拟结果.xlsx"):
    # 准备数据
    data = {
        "排名": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13],
        "队伍": ["南通", "南京", "盐城", "徐州", "泰州", "无锡", "苏州", 
                "连云港", "淮安", "镇江", "扬州", "宿迁", "常州"],
        "夺冠概率": ["98.5%", "1.2%", "0.2%", "0.1%", "0.0%", "0.0%", "0.0%", 
                   "0.0%", "0.0%", "0.0%", "0.0%", "0.0%", "0.0%"],
        "前三概率": ["100.0%", "85.4%", "57.2%", "47.7%", "6.3%", "2.2%", "1.3%", 
                  "0.0%", "0.0%", "0.0%", "0.0%", "0.0%", "0.0%"],
        "降级概率": ["0.0%", "0.0%", "0.0%", "0.0%", "0.0%", "0.7%", "0.4%", 
                  "17.0%", "28.5%", "43.3%", "44.7%", "66.6%", "98.8%"],
        "平均排名": [1.0, 2.5, 3.5, 3.5, 5.2, 6.4, 6.6, 8.9, 9.4, 10.1, 10.1, 11.0, 12.8],
        "平均积分": [31.3, 24.1, 22.0, 22.0, 19.0, 16.9, 16.2, 13.1, 12.8, 11.6, 11.8, 10.1, 7.2],
        "积分范围": ["24-36", "17-29", "16-28", "18-27", "14-23", "11-23", "11-23", 
                  "8-20", "8-20", "9-18", "7-19", "6-18", "4-13"],
        "最可能排名": [1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 12, 13]
    }
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 创建ExcelWriter对象，用于写入Excel文件
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 将数据写入Excel的第一个工作表
        df.to_excel(writer, sheet_name='蒙特卡洛模拟结果', index=False)
        
        # 获取工作表对象，以便设置格式
        worksheet = writer.sheets['蒙特卡洛模拟结果']
        
        # 设置列宽，使内容更好地显示
        column_widths = [5, 10, 10, 10, 10, 10, 10, 10, 10]
        for i, width in enumerate(column_widths, 1):
            worksheet.column_dimensions[chr(64 + i)].width = width
    
    print(f"Excel文件已生成: {filename}")

if __name__ == "__main__":
    generate_monte_carlo_excel()
