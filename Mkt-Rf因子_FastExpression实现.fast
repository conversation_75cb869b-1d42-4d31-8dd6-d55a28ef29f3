# 美股Mkt-Rf因子 Fast Expression 实现
# 适用于 USA 1 TOP3000 数据

# ========================================
# 方案1：基础市值加权市场收益率
# ========================================
# 计算市值加权市场收益率
market_return = ts_mean(returns * cap / ts_sum(cap, 20), 20)

# 使用常数作为无风险利率代理
risk_free_rate = 0.02

# Mkt-Rf因子
mkt_rf_factor_basic = market_return - risk_free_rate

# ========================================
# 方案2：基于波动率调整的市场风险因子
# ========================================
# 计算市场波动率
market_volatility = ts_std_dev(returns, 20)

# 市值加权收益率
weighted_returns = returns * cap / ts_sum(cap, 20)
market_return_weighted = ts_mean(weighted_returns, 20)

# 构建风险调整的Mkt-Rf因子
mkt_rf_factor_vol_adjusted = market_return_weighted * market_volatility

# ========================================
# 方案3：基于排名的Mkt-Rf因子
# ========================================
# 计算市值加权收益率排名
market_return_rank = rank(ts_mean(weighted_returns, 20))

# 计算波动率排名
volatility_rank = rank(ts_std_dev(returns, 20))

# 构建排名差异Mkt-Rf因子
mkt_rf_factor_rank = market_return_rank - volatility_rank

# ========================================
# 方案4：多时间窗口Mkt-Rf因子
# ========================================
# 短期市场收益率（5天）
short_market_return = ts_mean(returns * cap / ts_sum(cap, 5), 5)

# 中期市场收益率（20天）
medium_market_return = ts_mean(returns * cap / ts_sum(cap, 20), 20)

# 长期市场收益率（60天）
long_market_return = ts_mean(returns * cap / ts_sum(cap, 60), 60)

# 市场波动率
market_volatility_60 = ts_std_dev(returns, 20)

# 构建多时间窗口Mkt-Rf因子
mkt_rf_factor_multi_window = (0.5 * short_market_return + 
                              0.3 * medium_market_return + 
                              0.2 * long_market_return) * 
                             (1 - market_volatility_60)

# ========================================
# 方案5：Z-score标准化Mkt-Rf因子（推荐）
# ========================================
# 计算市值加权收益率
weighted_returns_zscore = returns * cap / ts_sum(cap, 20)

# 计算市场收益率的Z-score
market_return_zscore = ts_zscore(weighted_returns_zscore, 20)

# 计算波动率的Z-score
volatility_zscore = ts_zscore(ts_std_dev(returns, 20), 20)

# 构建标准化的Mkt-Rf因子
mkt_rf_factor_zscore = market_return_zscore - volatility_zscore

# ========================================
# 方案6：高级Mkt-Rf因子（结合多个指标）
# ========================================
# 价格稳定性指数（如果可用）
price_stability = anl49_pricestabilityindex

# 价格变化标准差
price_std = anl49_standarddeviationofpricechange

# 结合多个风险指标
risk_composite = (market_volatility + price_std) / 2

# 高级Mkt-Rf因子
mkt_rf_factor_advanced = market_return_zscore * (1 - risk_composite)

# ========================================
# 方案7：动态权重Mkt-Rf因子
# ========================================
# 根据市场波动率动态调整权重
market_vol_dynamic = ts_std_dev(returns, 20)
vol_weight = ts_scale(market_vol_dynamic, 20)

# 动态权重Mkt-Rf因子
mkt_rf_factor_dynamic = (1 - vol_weight) * market_return_zscore + vol_weight * volatility_zscore

# ========================================
# 方案8：行业中性化Mkt-Rf因子
# ========================================
# 基础Mkt-Rf因子
base_mkt_rf = market_return_zscore - volatility_zscore

# 行业中性化（需要行业分组数据）
# mkt_rf_factor_neutralized = group_neutralize(base_mkt_rf, industry_group)

# ========================================
# 最终推荐因子
# ========================================
# 主要推荐：Z-score标准化Mkt-Rf因子
mkt_rf_factor = mkt_rf_factor_zscore

# 备选方案：动态权重Mkt-Rf因子
mkt_rf_factor_alternative = mkt_rf_factor_dynamic

# ========================================
# 因子组合示例
# ========================================
# 结合价值因子
value_factor = rank(close / eps)
combined_factor_value = 0.7 * mkt_rf_factor + 0.3 * value_factor

# 结合动量因子
momentum_factor = ts_mean(returns, 20)
combined_factor_momentum = 0.5 * mkt_rf_factor + 0.5 * momentum_factor

# 结合质量因子
quality_factor = rank(income / assets)
combined_factor_quality = 0.6 * mkt_rf_factor + 0.4 * quality_factor 