# A矩阵动态调制实现方案

## 1. 核心目标

实现ProFoNet v2中状态转移矩阵A的动态调制机制，使其能够根据输入内容进行自适应调整，从而实现聚焦功能。

## 2. 技术方案

### 2.1 基础原理

原始Mamba中A矩阵是静态的：
```python
A = -torch.exp(self.A_log.float())  # 固定的状态转移矩阵
```

我们的改进：使A矩阵根据选通值g_t动态调制：
```python
A_modulated = g_t * A_base + (1 - g_t) * I
```

### 2.2 实现文件

创建新文件：`mamba_ssm/modules/dynamic_a_matrix.py`

## 3. 具体实现代码

### 3.1 动态A矩阵调制器

```python
import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
import copy

class DynamicAMatrixModulator(nn.Module):
    """
    动态状态转移矩阵A的调制器
    
    核心功能：
    1. 根据选通值动态调制A矩阵
    2. 保持数值稳定性
    3. 支持梯度反向传播
    4. 提供备份和恢复机制
    """
    
    def __init__(self, d_inner: int, d_state: int, device=None, dtype=None):
        super().__init__()
        self.d_inner = d_inner
        self.d_state = d_state
        
        # 基础A矩阵（从原始Mamba继承）
        A = torch.arange(1, d_state + 1, dtype=torch.float32, device=device)
        A = A.repeat(d_inner, 1)  # (d_inner, d_state)
        A_log = torch.log(A)
        self.A_log = nn.Parameter(A_log)
        self.A_log._no_weight_decay = True
        
        # 单位矩阵（用于调制）
        self.register_buffer('identity_matrix', 
                           torch.eye(d_state, dtype=torch.float32, device=device))
        
        # 备份机制
        self._backup_A_log = None
        self._backup_enabled = True
        
    def get_base_A_matrix(self) -> torch.Tensor:
        """获取基础A矩阵"""
        return -torch.exp(self.A_log.float())
    
    def modulate_A_matrix(self, gating_values: torch.Tensor) -> torch.Tensor:
        """
        动态调制A矩阵
        
        Args:
            gating_values: 选通值 (batch_size, 1) 或 (batch_size, d_inner)
            
        Returns:
            A_modulated: 调制后的A矩阵 (batch_size, d_inner, d_state)
        """
        # 获取基础A矩阵
        A_base = self.get_base_A_matrix()  # (d_inner, d_state)
        batch_size = gating_values.shape[0]
        
        # 处理选通值维度
        if gating_values.dim() == 2 and gating_values.shape[1] == 1:
            # 广播到所有维度
            g_expanded = gating_values.unsqueeze(-1).unsqueeze(-1)  # (batch, 1, 1, 1)
            g_expanded = g_expanded.expand(batch_size, self.d_inner, 1, 1)
        elif gating_values.dim() == 2 and gating_values.shape[1] == self.d_inner:
            # 每个维度独立的选通值
            g_expanded = gating_values.unsqueeze(-1).unsqueeze(-1)  # (batch, d_inner, 1, 1)
        else:
            raise ValueError(f"Invalid gating_values shape: {gating_values.shape}")
        
        # 扩展基础矩阵和单位矩阵
        A_base_expanded = A_base.unsqueeze(0).expand(batch_size, -1, -1)  # (batch, d_inner, d_state)
        I_expanded = self.identity_matrix.unsqueeze(0).unsqueeze(0).expand(
            batch_size, self.d_inner, -1, -1)  # (batch, d_inner, d_state, d_state)
        
        # 动态调制
        A_modulated = (g_expanded.squeeze(-1) * A_base_expanded + 
                      (1 - g_expanded.squeeze(-1)) * I_expanded.diagonal(dim1=-2, dim2=-1))
        
        # 数值稳定性检查
        self._check_numerical_stability(A_modulated, "A_modulated")
        
        return A_modulated
    
    def _check_numerical_stability(self, tensor: torch.Tensor, name: str):
        """检查数值稳定性"""
        if torch.isnan(tensor).any():
            raise ValueError(f"NaN detected in {name}")
        if torch.isinf(tensor).any():
            raise ValueError(f"Inf detected in {name}")
        if (tensor.abs() > 1e6).any():
            print(f"Warning: Large values detected in {name}, max: {tensor.abs().max()}")
    
    def create_backup(self):
        """创建A矩阵参数的备份"""
        if self._backup_enabled:
            self._backup_A_log = self.A_log.data.clone()
            print("A matrix backup created")
    
    def restore_from_backup(self):
        """从备份恢复A矩阵参数"""
        if self._backup_A_log is not None:
            self.A_log.data.copy_(self._backup_A_log)
            print("A matrix restored from backup")
        else:
            print("Warning: No backup available")
    
    def enable_backup(self, enabled: bool = True):
        """启用或禁用备份功能"""
        self._backup_enabled = enabled
    
    def get_modulation_stats(self, gating_values: torch.Tensor) -> dict:
        """获取调制统计信息"""
        with torch.no_grad():
            A_base = self.get_base_A_matrix()
            A_modulated = self.modulate_A_matrix(gating_values)
            
            stats = {
                'base_A_mean': A_base.mean().item(),
                'base_A_std': A_base.std().item(),
                'modulated_A_mean': A_modulated.mean().item(),
                'modulated_A_std': A_modulated.std().item(),
                'gating_mean': gating_values.mean().item(),
                'gating_std': gating_values.std().item(),
                'modulation_strength': (A_modulated - A_base.unsqueeze(0)).abs().mean().item()
            }
        return stats

class SimpleGatingNetwork(nn.Module):
    """
    简单的选通网络，用于生成选通值
    """
    
    def __init__(self, input_dim: int, hidden_dim: int = 64):
        super().__init__()
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()  # 确保输出在[0,1]范围内
        )
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: 输入特征 (batch_size, input_dim)
        Returns:
            gating_values: 选通值 (batch_size, 1)
        """
        return self.network(x)

# 使用示例和测试代码
def test_dynamic_a_matrix():
    """测试动态A矩阵调制功能"""
    print("Testing Dynamic A Matrix Modulation...")
    
    # 初始化参数
    batch_size = 4
    d_inner = 64
    d_state = 16
    
    # 创建调制器
    modulator = DynamicAMatrixModulator(d_inner, d_state)
    
    # 创建备份
    modulator.create_backup()
    
    # 生成测试选通值
    gating_values = torch.rand(batch_size, 1) * 0.8 + 0.1  # [0.1, 0.9]范围
    
    try:
        # 测试A矩阵调制
        A_base = modulator.get_base_A_matrix()
        A_modulated = modulator.modulate_A_matrix(gating_values)
        
        print(f"Base A matrix shape: {A_base.shape}")
        print(f"Modulated A matrix shape: {A_modulated.shape}")
        print(f"Gating values: {gating_values.flatten()}")
        
        # 获取统计信息
        stats = modulator.get_modulation_stats(gating_values)
        print("Modulation Statistics:")
        for key, value in stats.items():
            print(f"  {key}: {value:.6f}")
        
        # 测试梯度计算
        loss = A_modulated.sum()
        loss.backward()
        
        print(f"A_log gradient norm: {modulator.A_log.grad.norm().item():.6f}")
        
        print("✓ All tests passed!")
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        # 恢复备份
        modulator.restore_from_backup()
        raise

if __name__ == "__main__":
    test_dynamic_a_matrix()
```

## 4. 集成到现有Mamba代码

### 4.1 修改mamba_simple.py

在`mamba_ssm/modules/mamba_simple.py`中添加动态A矩阵支持：

```python
# 在文件开头添加导入
from .dynamic_a_matrix import DynamicAMatrixModulator, SimpleGatingNetwork

# 在Mamba类的__init__方法中添加
def __init__(self, ...):
    # ... 现有代码 ...
    
    # 添加动态A矩阵调制器
    self.use_dynamic_A = kwargs.get('use_dynamic_A', False)
    if self.use_dynamic_A:
        self.A_modulator = DynamicAMatrixModulator(
            self.d_inner, self.d_state, device=device, dtype=dtype
        )
        self.gating_network = SimpleGatingNetwork(self.d_model)
        # 创建初始备份
        self.A_modulator.create_backup()

# 修改forward方法
def forward(self, hidden_states, inference_params=None):
    # ... 现有代码直到A矩阵计算 ...
    
    if self.use_dynamic_A and self.training:
        # 生成选通值
        # 假设hidden_states是(B, L, D)，我们取平均作为特征
        input_features = hidden_states.mean(dim=1)  # (B, D)
        gating_values = self.gating_network(input_features)  # (B, 1)
        
        # 使用动态调制的A矩阵
        A = self.A_modulator.modulate_A_matrix(gating_values)
        # A现在是(B, d_inner, d_state)，需要适配到现有的selective_scan_fn
        
    else:
        # 使用原始的静态A矩阵
        A = -torch.exp(self.A_log.float())
    
    # ... 继续现有的forward逻辑 ...
```

## 5. 备份和恢复机制

### 5.1 自动备份策略

```python
class AutoBackupManager:
    """自动备份管理器"""
    
    def __init__(self, modulator: DynamicAMatrixModulator, backup_interval: int = 100):
        self.modulator = modulator
        self.backup_interval = backup_interval
        self.step_count = 0
        self.backup_history = []
    
    def step(self):
        """每次训练步骤调用"""
        self.step_count += 1
        if self.step_count % self.backup_interval == 0:
            self.create_checkpoint()
    
    def create_checkpoint(self):
        """创建检查点"""
        checkpoint = {
            'step': self.step_count,
            'A_log': self.modulator.A_log.data.clone(),
            'timestamp': torch.tensor(time.time())
        }
        self.backup_history.append(checkpoint)
        print(f"Checkpoint created at step {self.step_count}")
    
    def restore_checkpoint(self, step: int = -1):
        """恢复到指定检查点"""
        if not self.backup_history:
            print("No checkpoints available")
            return
        
        checkpoint = self.backup_history[step]
        self.modulator.A_log.data.copy_(checkpoint['A_log'])
        print(f"Restored to checkpoint at step {checkpoint['step']}")
```

## 6. 使用说明

### 6.1 基本使用

```python
# 创建带动态A矩阵的Mamba模型
model = Mamba(d_model=256, use_dynamic_A=True)

# 训练时会自动使用动态调制
output = model(input_data)

# 获取调制统计信息
if hasattr(model, 'A_modulator'):
    stats = model.A_modulator.get_modulation_stats(gating_values)
    print(stats)
```

### 6.2 备份管理

```python
# 手动创建备份
model.A_modulator.create_backup()

# 训练过程中如果出现问题，恢复备份
try:
    # 训练代码
    loss.backward()
    optimizer.step()
except:
    model.A_modulator.restore_from_backup()
    print("Restored from backup due to training instability")
```

这个实现方案专注于A矩阵的动态调制功能，包含了完整的备份机制，可以确保训练过程的稳定性和可恢复性。
