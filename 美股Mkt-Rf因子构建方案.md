# 美股Mkt-Rf因子构建方案

## 因子定义
Mkt-Rf因子 = 市场收益率 - 无风险收益率
这是CAPM模型中的核心因子，用于衡量市场风险溢价。

## 数据库支持的数据字段

### 基础价格数据
- `close`: 日收盘价
- `open`: 日开盘价  
- `high`: 日最高价
- `low`: 日最低价
- `volume`: 日成交量
- `vwap`: 成交量加权平均价格
- `returns`: 日收益率

### 市值数据
- `cap`: 日市值（百万美元）
- `mdl26_market_cap_u`: 美元市值
- `mdl26_market_cap_l`: 本地货币市值

### 风险相关数据
- `anl49_standarddeviationofpricechange`: 价格变化标准差（风险度量）
- `anl49_pricestabilityindex`: 价格稳定性指数

## Fast Expression语法实现

### 方案1：基于市值加权的市场收益率
```fast
# 计算市值加权市场收益率
market_return = ts_mean(returns * cap / ts_sum(cap, 20), 20)

# 使用短期国债收益率作为无风险利率代理（需要外部数据）
# 这里使用常数0.02作为无风险利率示例
risk_free_rate = 0.02

# Mkt-Rf因子
mkt_rf_factor = market_return - risk_free_rate
```

### 方案2：基于价格变化的市场风险因子
```fast
# 计算市场波动率（风险代理）
market_volatility = ts_std_dev(returns, 20)

# 计算市值加权平均收益率
weighted_returns = returns * cap / ts_sum(cap, 20)
market_return = ts_mean(weighted_returns, 20)

# 构建Mkt-Rf因子（结合收益率和波动率）
mkt_rf_factor = market_return * market_volatility
```

### 方案3：基于排名和标准化的Mkt-Rf因子
```fast
# 计算市值加权收益率
weighted_returns = returns * cap / ts_sum(cap, 20)

# 计算市场收益率排名
market_return_rank = rank(ts_mean(weighted_returns, 20))

# 计算波动率排名
volatility_rank = rank(ts_std_dev(returns, 20))

# 构建Mkt-Rf因子
mkt_rf_factor = market_return_rank - volatility_rank
```

### 方案4：高级Mkt-Rf因子（使用多个时间窗口）
```fast
# 短期市场收益率（5天）
short_market_return = ts_mean(returns * cap / ts_sum(cap, 5), 5)

# 中期市场收益率（20天）
medium_market_return = ts_mean(returns * cap / ts_sum(cap, 20), 20)

# 长期市场收益率（60天）
long_market_return = ts_mean(returns * cap / ts_sum(cap, 60), 60)

# 市场波动率
market_volatility = ts_std_dev(returns, 20)

# 构建多时间窗口Mkt-Rf因子
mkt_rf_factor = (0.5 * short_market_return + 
                 0.3 * medium_market_return + 
                 0.2 * long_market_return) * 
                (1 - market_volatility)
```

### 方案5：基于Z-score标准化的Mkt-Rf因子
```fast
# 计算市值加权收益率
weighted_returns = returns * cap / ts_sum(cap, 20)

# 计算市场收益率的Z-score
market_return_zscore = ts_zscore(weighted_returns, 20)

# 计算波动率的Z-score
volatility_zscore = ts_zscore(ts_std_dev(returns, 20), 20)

# 构建标准化的Mkt-Rf因子
mkt_rf_factor = market_return_zscore - volatility_zscore
```

## 推荐实现方案

### 主要推荐：方案5（Z-score标准化）
```fast
# 最终推荐的Mkt-Rf因子实现
weighted_returns = returns * cap / ts_sum(cap, 20)
market_return_zscore = ts_zscore(weighted_returns, 20)
volatility_zscore = ts_zscore(ts_std_dev(returns, 20), 20)
mkt_rf_factor = market_return_zscore - volatility_zscore
```

### 优势：
1. **标准化处理**：使用Z-score确保因子值在合理范围内
2. **风险调整**：结合收益率和波动率，体现风险溢价概念
3. **市值加权**：反映大市值股票对市场的影响
4. **时间序列稳定性**：使用20天窗口提供稳定的信号

## 参数调优建议

### 时间窗口选择
- **短期（5-10天）**：捕捉短期市场情绪变化
- **中期（20-30天）**：平衡信号稳定性和时效性
- **长期（60-120天）**：反映长期市场趋势

### 权重调整
- 可根据市场环境调整收益率和波动率的权重
- 牛市可增加收益率权重，熊市可增加波动率权重

## 使用注意事项

1. **数据质量**：确保close、returns、cap字段的完整性
2. **异常值处理**：使用ts_zscore等操作符处理异常值
3. **行业中性化**：可考虑使用group_neutralize进行行业中性化
4. **回测验证**：建议进行充分的历史回测验证因子有效性

## 扩展应用

### 与其他因子结合
```fast
# 结合价值因子
value_factor = rank(close / eps)
combined_factor = 0.7 * mkt_rf_factor + 0.3 * value_factor

# 结合动量因子
momentum_factor = ts_mean(returns, 20)
combined_factor = 0.5 * mkt_rf_factor + 0.5 * momentum_factor
```

### 动态权重调整
```fast
# 根据市场波动率动态调整权重
market_vol = ts_std_dev(returns, 20)
vol_weight = ts_scale(market_vol, 20)
mkt_rf_factor = (1 - vol_weight) * market_return_zscore + vol_weight * volatility_zscore
``` 