# Mkt-Rf因子优化方案
# 基于经济学分析和数据库可用字段

# ========================================
# 原始因子（需要优化）
# ========================================
# 计算市场波动率（风险代理）
market_volatility = ts_std_dev(returns, 20);

# 计算市值加权平均收益率
weighted_returns = returns * cap / ts_sum(cap, 20);
market_return = ts_mean(weighted_returns, 20);

# 原始Mkt-Rf因子（波动较大）
original_mkt_rf = -market_return * market_volatility;

# ========================================
# 优化方案1：信号平滑 + 异常值处理
# ========================================
# 使用winsorize处理异常值
smoothed_returns = winsorize(weighted_returns, std=3);
smoothed_volatility = winsorize(market_volatility, std=3);

# 多时间窗口平滑
short_smooth = ts_mean(smoothed_returns, 5);
medium_smooth = ts_mean(smoothed_returns, 20);
long_smooth = ts_mean(smoothed_returns, 60);

# 加权平滑信号
smoothed_market_return = 0.2 * short_smooth + 0.5 * medium_smooth + 0.3 * long_smooth;
smoothed_market_vol = ts_mean(smoothed_volatility, 20);

# 优化后的Mkt-Rf因子
optimized_mkt_rf_1 = -smoothed_market_return * smoothed_market_vol;

# ========================================
# 优化方案2：基于市值的分组优化
# ========================================
# 使用市值创建分组
market_cap_buckets = bucket(rank(cap), range="0, 1, 0.2");  # 5个市值分组

# 在市值分组内计算因子
group_market_return = group_mean(smoothed_market_return, cap, market_cap_buckets);
group_market_vol = group_mean(smoothed_market_vol, cap, market_cap_buckets);

# 分组优化后的Mkt-Rf因子
optimized_mkt_rf_2 = -group_market_return * group_market_vol;

# ========================================
# 优化方案3：波动率调整的平滑因子
# ========================================
# 计算波动率的Z-score
volatility_zscore = ts_zscore(market_volatility, 20);

# 使用波动率Z-score调整权重
volatility_weight = ts_scale(volatility_zscore, 20);

# 波动率调整的Mkt-Rf因子
vol_adjusted_return = smoothed_market_return * (1 - volatility_weight);
optimized_mkt_rf_3 = -vol_adjusted_return * smoothed_market_vol;

# ========================================
# 优化方案4：多因子组合优化
# ========================================
# 计算多个风险指标
price_volatility = ts_std_dev(close / ts_mean(close, 20), 20);
volume_volatility = ts_std_dev(volume / ts_mean(volume, 20), 20);

# 综合风险指标
composite_risk = (market_volatility + price_volatility + volume_volatility) / 3;

# 风险调整的收益率
risk_adjusted_return = smoothed_market_return / (1 + composite_risk);

# 多因子优化后的Mkt-Rf因子
optimized_mkt_rf_4 = -risk_adjusted_return * smoothed_market_vol;

# ========================================
# 优化方案5：动态权重优化
# ========================================
# 计算市场状态指标
market_trend = ts_mean(returns, 60);  # 长期趋势
market_momentum = ts_mean(returns, 20);  # 中期动量

# 动态权重
trend_weight = ts_scale(market_trend, 60);
momentum_weight = ts_scale(market_momentum, 20);

# 动态调整的Mkt-Rf因子
dynamic_return = smoothed_market_return * (1 + 0.3 * trend_weight + 0.2 * momentum_weight);
optimized_mkt_rf_5 = -dynamic_return * smoothed_market_vol;

# ========================================
# 优化方案6：基于排名的稳健因子
# ========================================
# 计算排名
return_rank = rank(smoothed_market_return);
volatility_rank = rank(smoothed_market_vol);

# 排名差异因子
rank_diff = return_rank - volatility_rank;

# 稳健的Mkt-Rf因子
optimized_mkt_rf_6 = -rank_diff * smoothed_market_vol;

# ========================================
# 优化方案7：信号过滤优化
# ========================================
# 计算信号强度
signal_strength = abs(smoothed_market_return) / smoothed_market_vol;

# 过滤弱信号
strong_signal_threshold = ts_mean(signal_strength, 20);
filtered_return = if_else(signal_strength > strong_signal_threshold, 
                         smoothed_market_return, 
                         0.5 * smoothed_market_return);

# 信号过滤后的Mkt-Rf因子
optimized_mkt_rf_7 = -filtered_return * smoothed_market_vol;

# ========================================
# 最终推荐优化方案
# ========================================
# 组合多个优化方案
final_mkt_rf = (0.3 * optimized_mkt_rf_1 + 
                0.2 * optimized_mkt_rf_3 + 
                0.2 * optimized_mkt_rf_4 + 
                0.2 * optimized_mkt_rf_5 + 
                0.1 * optimized_mkt_rf_7);

# 最终标准化
final_mkt_rf_normalized = ts_zscore(final_mkt_rf, 20);

# ========================================
# 备选方案：基于经济周期的优化
# ========================================
# 经济周期指标（基于收益率波动）
economic_cycle = ts_std_dev(returns, 60) / ts_mean(ts_std_dev(returns, 60), 60);

# 周期调整的Mkt-Rf因子
cycle_adjusted_return = smoothed_market_return * (1 - 0.5 * economic_cycle);
optimized_mkt_rf_cycle = -cycle_adjusted_return * smoothed_market_vol;

# ========================================
# 风险控制版本
# ========================================
# 最大回撤控制
max_drawdown = ts_min(final_mkt_rf_normalized, 20);
risk_controlled_mkt_rf = if_else(max_drawdown < -0.5, 
                                0.5 * final_mkt_rf_normalized, 
                                final_mkt_rf_normalized); 