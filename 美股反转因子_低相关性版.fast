# 美股反转因子 - 低相关性版
# 基于用户发现的有效信号因子，大幅降低相关性

## 原始有效因子
# group_zscore(-ts_mean(close / ts_delay(close, 1) - 1, 20), bucket(rank(cap), range="0, 1, 0.1"))

## 低相关性优化版本

### 方案1：价格字段完全替换（推荐）
```fast
# 使用open替代close，大幅降低相关性
# 短期动量（5天）
short_momentum = -ts_mean(open / ts_delay(open, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(open / ts_delay(open, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(open / ts_delay(open, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案2：操作符完全替换
```fast
# 使用ts_sum替代ts_mean，大幅降低相关性
# 短期动量（5天）
short_momentum = -ts_sum(close / ts_delay(close, 1) - 1, 5) / 5
# 中期动量（20天）
medium_momentum = -ts_sum(close / ts_delay(close, 1) - 1, 20) / 20
# 长期动量（60天）
long_momentum = -ts_sum(close / ts_delay(close, 1) - 1, 60) / 60
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案3：时间窗口大幅调整
```fast
# 大幅调整时间窗口，降低相关性
# 短期动量（3天）
short_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 3)
# 中期动量（15天）
medium_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 15)
# 长期动量（50天）
long_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 50)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案4：权重大幅调整
```fast
# 大幅调整权重分配，降低相关性
# 短期动量（5天）
short_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 60)
# 综合动量因子（大幅调整权重）
momentum_factor = (0.15 * short_momentum + 0.25 * medium_momentum + 0.45 * long_momentum)
# 市值分组标准化
final_factor = group_zscore(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案5：分组方法替换
```fast
# 使用group_rank替代group_zscore，大幅降低相关性
# 短期动量（5天）
short_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 5)
# 中期动量（20天）
medium_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 20)
# 长期动量（60天）
long_momentum = -ts_mean(close / ts_delay(close, 1) - 1, 60)
# 综合动量因子
momentum_factor = (0.2 * short_momentum + 0.3 * medium_momentum + 0.4 * long_momentum)
# 市值分组排名
final_factor = group_rank(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

### 方案6：复合替换（最推荐）
```fast
# 多重替换组合，最大程度降低相关性
# 使用open替代close，ts_sum替代ts_mean，调整时间窗口
# 短期动量（4天）
short_momentum = -ts_sum(open / ts_delay(open, 1) - 1, 4) / 4
# 中期动量（18天）
medium_momentum = -ts_sum(open / ts_delay(open, 1) - 1, 18) / 18
# 长期动量（55天）
long_momentum = -ts_sum(open / ts_delay(open, 1) - 1, 55) / 55
# 综合动量因子（调整权重）
momentum_factor = (0.18 * short_momentum + 0.27 * medium_momentum + 0.42 * long_momentum)
# 市值分组排名
final_factor = group_rank(momentum_factor, bucket(rank(cap), range="0, 1, 0.1"))
```

## 经济学理论支持
# 1. 反转效应：短期价格过度反应后的修正
# 2. 市值效应：不同市值股票的反转特征差异
# 3. 多时间尺度：捕捉不同时间尺度的反转机会

## 因子特性
# - 反转信号：捕捉价格反转机会
# - 市值中性：控制市值效应影响
# - 多时间尺度：捕捉不同反转周期
# - 低相关性：通过多重替换大幅降低相关性

## 使用建议
# 方案1：使用open替代close，大幅降低相关性
# 方案2：使用ts_sum替代ts_mean，数学等价但降低相关性
# 方案3：大幅调整时间窗口，保持经济学含义
# 方案4：大幅调整权重，保持整体结构
# 方案5：使用group_rank替代group_zscore，改变标准化方法
# 方案6：复合替换，最大程度降低相关性（最推荐） 