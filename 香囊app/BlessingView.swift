//
//  BlessingView.swift
//  香囊app
//
//  Created by AI on 2025/07/06.
//

import SwiftUI

// MARK: - Data Models
struct Pendant: Identifiable {
    let id = UUID()
    let imageURL: URL?
    let imageName: String? // 新增：本地图片名称
    let placeholder: AnyView
    let size: CGFloat // Represents a fraction of the orbit's diameter
    let angle: Double
    let isInnerCircle: Bool
    
    init(imageURL: URL, size: CGFloat, angle: Double, isInnerCircle: Bool) {
        self.imageURL = imageURL
        self.imageName = nil
        self.placeholder = AnyView(Circle().fill(Color.white.opacity(0.1)))
        self.size = size
        self.angle = angle
        self.isInnerCircle = isInnerCircle
    }
    
    init(placeholder: AnyView, size: CGFloat, angle: Double, isInnerCircle: Bool) {
        self.imageURL = nil
        self.imageName = nil
        self.placeholder = placeholder
        self.size = size
        self.angle = angle
        self.isInnerCircle = isInnerCircle
    }
    
    // 新增：本地图片初始化方法
    init(imageName: String, size: CGFloat, angle: Double, isInnerCircle: Bool) {
        self.imageURL = nil
        self.imageName = imageName
        self.placeholder = AnyView(Circle().fill(Color.white.opacity(0.1)))
        self.size = size
        self.angle = angle
        self.isInnerCircle = isInnerCircle
    }
}

// MARK: - Main View
struct BlessingView: View {
    var body: some View {
        ZStack {
            // Background
            Color(red: 33/255, green: 17/255, blue: 52/255).ignoresSafeArea()
            
            // Auto Layout using VStack
            VStack(spacing: 0) {
                // Top bar removed as per request
                
                Spacer(minLength: 20)
                
                BlessingContentView()
                
                // This flexible spacer pushes all subsequent views to the bottom.
                Spacer()
                
                BottomContentView()
                    .padding(.bottom, 50) // 增加与导航栏的间距
                
                BlessingBottomNavBar()
                    .padding(.bottom, 0) // 进一步下移导航栏
            }
            .padding(.horizontal)
        }
    }
}

// MARK: - Subviews (Refactored for Auto Layout)

private struct BlessingContentView: View {
    @State private var outerRotation: Double = 0
    @State private var innerRotation: Double = 0
    @State private var hoveredPendantId: UUID? = nil // 追踪悬停的香囊

    private let pendants: [Pendant] = [
        // Outer Circle Pendants - 减少数量，调整大小和亮度
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.9))), size: 0.06, angle: 30, isInnerCircle: false),
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.5))), size: 0.04, angle: 80, isInnerCircle: false),
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.7))), size: 0.08, angle: 130, isInnerCircle: false),
        // 使用本地香囊图片 - 大小增加3倍
        Pendant(imageName: "Sachet1", size: 0.45, angle: 180, isInnerCircle: false),
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.6))), size: 0.05, angle: 230, isInnerCircle: false),
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.8))), size: 0.07, angle: 280, isInnerCircle: false),
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.4))), size: 0.03, angle: 330, isInnerCircle: false),

        // Inner Circle Pendants - 减少数量，调整大小和亮度
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.8))), size: 0.06, angle: 45, isInnerCircle: true),
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.5))), size: 0.04, angle: 90, isInnerCircle: true),
        // 使用本地香囊图片 - 大小增加3倍
        Pendant(imageName: "Sachet2", size: 0.36, angle: 135, isInnerCircle: true),
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.7))), size: 0.05, angle: 180, isInnerCircle: true),
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.9))), size: 0.07, angle: 225, isInnerCircle: true),
        // 使用本地香囊图片 - 大小增加3倍
        Pendant(imageName: "Sachet3", size: 0.42, angle: 270, isInnerCircle: true),
        Pendant(placeholder: AnyView(Circle().fill(Color.white.opacity(0.6))), size: 0.05, angle: 315, isInnerCircle: true),
    ]

    var body: some View {
        GeometryReader { geo in
            let center = CGPoint(x: geo.size.width / 2, y: geo.size.height / 2)
            let outerOrbitDiameter = min(geo.size.width, geo.size.height) * 0.9
            let innerOrbitDiameter = outerOrbitDiameter * 0.55
            
            ZStack {
                // Outer Orbit
                ZStack {
                    Circle().stroke(Color.white.opacity(0.2), lineWidth: 1)
                    ForEach(pendants.filter { !$0.isInnerCircle }) { pendant in
                        PendantView(
                            pendant: pendant, 
                            orbitDiameter: outerOrbitDiameter, 
                            rotationAngle: outerRotation,
                            isHovered: hoveredPendantId == pendant.id
                        )
                        .position(x: outerOrbitDiameter / 2, y: outerOrbitDiameter / 2)
                        .offset(x: (outerOrbitDiameter / 2) * cos(pendant.angle * .pi / 180),
                                y: (outerOrbitDiameter / 2) * sin(pendant.angle * .pi / 180))
                        .onTapGesture {
                            if pendant.imageName != nil {
                                hoveredPendantId = (hoveredPendantId == pendant.id) ? nil : pendant.id
                            }
                        }
                    }
                }
                .frame(width: outerOrbitDiameter, height: outerOrbitDiameter)
                .rotationEffect(.degrees(hoveredPendantId != nil ? 0 : outerRotation))
                
                // Inner Orbit
                ZStack {
                    Circle().stroke(Color.white.opacity(0.2), lineWidth: 1)
                    ForEach(pendants.filter { $0.isInnerCircle }) { pendant in
                        PendantView(
                            pendant: pendant, 
                            orbitDiameter: innerOrbitDiameter, 
                            rotationAngle: innerRotation,
                            isHovered: hoveredPendantId == pendant.id
                        )
                        .position(x: innerOrbitDiameter / 2, y: innerOrbitDiameter / 2)
                        .offset(x: (innerOrbitDiameter / 2) * cos(pendant.angle * .pi / 180),
                                y: (innerOrbitDiameter / 2) * sin(pendant.angle * .pi / 180))
                        .onTapGesture {
                            if pendant.imageName != nil {
                                hoveredPendantId = (hoveredPendantId == pendant.id) ? nil : pendant.id
                            }
                        }
                    }
                }
                .frame(width: innerOrbitDiameter, height: innerOrbitDiameter)
                .rotationEffect(.degrees(hoveredPendantId != nil ? 0 : innerRotation))
            }
            .position(center)
        }
        .onAppear {
            startRotationAnimation()
        }
        .onChange(of: hoveredPendantId) { _ in
            if hoveredPendantId == nil {
                startRotationAnimation()
            }
        }
    }
    
    private func startRotationAnimation() {
        withAnimation(.linear(duration: 45).repeatForever(autoreverses: false)) { 
            outerRotation = 360 
        }
        withAnimation(.linear(duration: 30).repeatForever(autoreverses: false)) { 
            innerRotation = -360 
        }
    }
}

private struct PendantView: View {
    let pendant: Pendant
    let orbitDiameter: CGFloat
    let rotationAngle: Double
    let isHovered: Bool

    var body: some View {
        let frameSize = orbitDiameter * pendant.size
        let scaleFactor: CGFloat = isHovered ? 1.3 : 1.0 // 悬停时放大1.3倍
        
        ZStack {
            if let imageName = pendant.imageName {
                // 使用本地图片 - 香囊保持竖直
                Image(imageName)
                    .resizable()
                    .scaledToFit()
                    .rotationEffect(.degrees(-rotationAngle)) // 反向旋转保持竖直
            } else if let url = pendant.imageURL {
                // 使用远程图片
                AsyncImage(url: url) { image in
                    image.resizable().scaledToFit()
                } placeholder: {
                    pendant.placeholder
                }
            } else {
                // 使用占位符 - 光点不需要保持竖直
                pendant.placeholder
            }
        }
        .frame(width: frameSize, height: frameSize)
        .scaleEffect(scaleFactor)
        .shadow(color: .purple.opacity(0.7), radius: frameSize * 0.15, x: 0, y: 0)
        .shadow(color: .white, radius: frameSize * 0.1, x: 0, y: 0)
        .animation(.easeInOut(duration: 0.3), value: isHovered)
    }
}

private struct BottomContentView: View {
    var body: some View {
        VStack(spacing: 8) {
            Text("点击香囊")
                .font(.title3)
                .fontWeight(.light)
            Text("看看大家写了什么吧...")
                .font(.subheadline)
                .opacity(0.8)
            
            Spacer().frame(height: 15)
            
            Button(action: {}) {
                Text("投送祝福")
                    .fontWeight(.medium)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        LinearGradient(gradient: Gradient(stops: [
                            .init(color: Color(red: 132/255, green: 238/255, blue: 245/255), location: 0),
                            .init(color: Color(red: 197/255, green: 132/255, blue: 245/255), location: 1)
                        ]), startPoint: .leading, endPoint: .trailing)
                    )
                    .cornerRadius(25)
            }
        }
        .foregroundColor(.white)
    }
}

private struct BlessingBottomNavBar: View {
    var body: some View {
        HStack {
            Image(systemName: "circle.grid.3x3.fill")
            Spacer()
            Image(systemName: "envelope.fill")
            Spacer()
            Image(systemName: "arrowshape.turn.up.right.fill")
            Spacer()
            Image(systemName: "person.fill")
        }
        .font(.title2)
        .foregroundColor(.white.opacity(0.6))
    }
}

#Preview {
    BlessingView()
} 