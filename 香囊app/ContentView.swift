//
//  ContentView.swift
//  香囊app
//
//  Created by 柯懿展 on 2025/7/6.
//

import SwiftUI

// MARK: - 全局布局常量 (基于 iPhone Pro Max: 430x932pt)

private struct LayoutConstants {
    // 基于 iPhone Pro Max (e.g., 161.03mm x 75.57mm): 430 x 932 pt
    static let screenWidth: CGFloat = 430
    static let screenHeight: CGFloat = 932
    
    // 卡片 - 调整宽度为65%并向右偏移
    static let cardWidth: CGFloat = screenWidth * 0.65 // ~279.5pt，更窄
    static let cardHeight: CGFloat = cardWidth * 1.8 // 拉长卡片高度比例
    
    // 间距 - 优化布局
    static let topToCardSpacing: CGFloat = 60
    static let cardToButtonSpacing: CGFloat = 45
    static let buttonToNavSpacing: CGFloat = 45
}

// MARK: - 数据模型

struct Card: Identifiable {
    let id = UUID()
    let imageName: String // 未来用于替换图片
    let text: String
}

// MARK: - 主视图

struct ContentView: View {
    // 卡片示例数据
    @State private var cards: [Card] = [
        Card(imageName: "Sachet1", text: "祝我们此刻来年都幸福..."),
        Card(imageName: "Sachet2", text: "愿你前程似锦，一帆风顺。"),
        Card(imageName: "Sachet3", text: "凤凰涅槃，浴火重生。")
    ]
    
    // 追踪当前顶层卡片的索引
    @State private var currentIndex: Int = 0
    
    // 按钮旋转角度
    @State private var buttonRotation: Double = 0

    // 渐变背景色
    private let backgroundGradient = LinearGradient(
        gradient: Gradient(colors: [
            Color(red: 45/255, green: 25/255, blue: 70/255),
            Color(red: 35/255, green: 15/255, blue: 60/255),
            Color(red: 55/255, green: 35/255, blue: 80/255)
        ]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )

    var body: some View {
        ZStack {
            backgroundGradient.ignoresSafeArea()
            
            // 返回按钮 - 左上角
            VStack {
                HStack {
                    Button(action: {
                        // 返回操作
                    }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 24, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(width: 40, height: 40)
                            .background(Color.white.opacity(0.15))
                            .clipShape(Circle())
                    }
                    .padding(.leading, 30)
                    
                    Spacer()
                }
                .padding(.top, LayoutConstants.topToCardSpacing - 20)
                
                Spacer()
            }

            VStack(spacing: 0) {
                // 移除顶部栏
                
                Spacer()
                    .frame(height: LayoutConstants.topToCardSpacing + 50 )
                
                // 卡片堆叠视图
                ZStack {
                    ForEach(cards.indices, id: \.self) { index in
                        CardView(card: cards[index])
                            .modifier(CardStackModifier(index: index, currentIndex: currentIndex, cardCount: cards.count))
                    }
                }
                .frame(width: LayoutConstants.cardWidth, height: LayoutConstants.cardHeight)
                .offset(x: 15) // 向右移动一点
                
                Spacer()
                    .frame(height: LayoutConstants.cardToButtonSpacing )

                // 刷新按钮
                Button(action: {
                    withAnimation(.spring(response: 1.2, dampingFraction: 0.8)) {
                        currentIndex = (currentIndex + 1) % cards.count
                        buttonRotation += 360 // 每次点击旋转360度
                    }
                }) {
                    Image("RefreshButton")
                        .resizable()
                        .scaledToFit()
                        .frame(width: 64, height: 64)
                        .rotationEffect(.degrees(buttonRotation))
                        .animation(.spring(response: 1.2, dampingFraction: 0.8), value: buttonRotation)
                }
                
                Spacer()
                     .frame(height: LayoutConstants.buttonToNavSpacing - 30)

                // 底部导航栏
                BottomNavBar()
                
                // 底部安全区域
                 Spacer()
                    .frame(height: 0)
            }
        }
    }
}

// MARK: - 卡片堆叠修改器 (优化版)

struct CardStackModifier: ViewModifier {
    let index: Int
    let currentIndex: Int
    let cardCount: Int
    
    private var stackPosition: Int {
        (index - currentIndex + cardCount) % cardCount
    }
    
    private var opacity: Double {
        switch stackPosition {
        case 0: return 1.0
        case 1: return 0.7
        case 2: return 0.4
        default: return 0.0
        }
    }
    
    private var scale: CGFloat {
        let scaleStep: CGFloat = 0.08
        return 1.0 - (CGFloat(stackPosition) * scaleStep)
    }
    
    private var rotation: Angle {
        let rotationStep: Double = 7.0
        return stackPosition != 0 ? .degrees(Double(stackPosition) * -rotationStep) : .degrees(0)
    }
    
    private var offset: CGSize {
        return CGSize(
            width: CGFloat(stackPosition) * -30, // 增加X轴偏移
            height: CGFloat(stackPosition) * -18  // 增加Y轴偏移
        )
    }

    func body(content: Content) -> some View {
        content
            .scaleEffect(scale)
            .offset(offset)
            .rotationEffect(rotation, anchor: .center)
            .opacity(opacity)
            .zIndex(-Double(stackPosition))
    }
}


// MARK: - 卡片视图 (根据Figma参数调整)

struct CardView: View {
    let card: Card
    
    // Figma中的渐变色
    let gradientColors = [
        Color(red: 123/255, green: 46/255, blue: 176/255),
        Color(red: 197/255, green: 132/255, blue: 245/255),
        Color(red: 218/255, green: 206/255, blue: 225/255),
        Color(red: 13/255, green: 41/255, blue: 134/255)
    ]

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景渐变
                RoundedRectangle(cornerRadius: geometry.size.width * 0.18) // 增加圆角
                    .fill(LinearGradient(gradient: Gradient(colors: gradientColors), startPoint: .topLeading, endPoint: .bottomTrailing))
                
        VStack {
                    // 心形按钮
                    HStack {
                        Spacer()
                        Image(systemName: "heart")
                            .font(.system(size: geometry.size.width * 0.08, weight: .bold))
                            .foregroundColor(.white)
                            .padding(geometry.size.width * 0.08)
                    }
                    
                    Spacer()
                    
                    // 香囊图片
                    ZStack {
                        Image(card.imageName)
                            .resizable()
                            .scaledToFit()
                            .frame(width: geometry.size.width * 0.9) // 增大香囊尺寸到90%
                            .scaleEffect(1.1) // 稍微放大
                            .shadow(color: .black.opacity(0.3), radius: 8, x: 0, y: 4)
                        
                        // 闪烁光点效果
                        SparkleEffectView()
                    }
                    
                    Spacer()
                    
                    // 祝福语
                    Text(card.text)
                        .font(.system(size: geometry.size.width * 0.06, weight: .semibold))
                        .foregroundColor(.white)
                        .padding(.bottom, geometry.size.height * 0.1)
                }
            }
        }
        .compositingGroup()
        .shadow(color: .black.opacity(0.35), radius: 20, x: 0, y: 10)
    }
}


// MARK: - 底部和顶部栏

struct BottomNavBar: View {
    var body: some View {
        HStack {
            TabBarIcon(iconName: "square.stack.3d.up.fill")
            Spacer()
            TabBarIcon(iconName: "envelope.fill")
            Spacer()
            TabBarIcon(iconName: "arrowshape.turn.up.right.fill")
            Spacer()
            TabBarIcon(iconName: "magnifyingglass")
        }
        .padding(.horizontal, 60) // 将底部导航图标推向两侧
        .frame(maxWidth: .infinity)
        .frame(height: 50)
        .background(Color(red: 45/255, green: 25/255, blue: 70/255).opacity(0.9))
    }
}

// MARK: - 底部导航图标 (无变化)

struct TabBarIcon: View {
    let iconName: String
    
    var body: some View {
        Image(systemName: iconName)
            .font(.system(size: 24, weight: .regular))
            .foregroundColor(.white.opacity(0.7))
    }
}


// MARK: - 闪烁光点效果

struct SparkleEffectView: View {
    @State private var sparkles: [SparkleData] = []
    
    var body: some View {
        ZStack {
            ForEach(sparkles, id: \.id) { sparkle in
                Circle()
                    .fill(Color.white.opacity(sparkle.opacity))
                    .frame(width: sparkle.size, height: sparkle.size)
                    .position(sparkle.position)
                    .scaleEffect(sparkle.scale)
                    .opacity(sparkle.opacity)
                    .animation(.easeInOut(duration: sparkle.duration), value: sparkle.opacity)
                    .animation(.easeInOut(duration: sparkle.duration), value: sparkle.scale)
            }
        }
        .onAppear {
            generateSparkles()
            startSparkleAnimation()
        }
    }
    
    private func generateSparkles() {
        sparkles = (0..<8).map { _ in
            SparkleData(
                position: CGPoint(
                    x: CGFloat.random(in: 50...200),
                    y: CGFloat.random(in: 50...200)
                ),
                size: CGFloat.random(in: 2...4),
                opacity: 0.0,
                scale: 0.0,
                duration: Double.random(in: 1.5...3.0)
            )
        }
    }
    
    private func startSparkleAnimation() {
        Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { _ in
            animateRandomSparkle()
        }
    }
    
    private func animateRandomSparkle() {
        guard !sparkles.isEmpty else { return }
        let randomIndex = Int.random(in: 0..<sparkles.count)
        
        withAnimation(.easeInOut(duration: sparkles[randomIndex].duration)) {
            sparkles[randomIndex].opacity = Double.random(in: 0.3...0.7)
            sparkles[randomIndex].scale = Double.random(in: 0.5...1.2)
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + sparkles[randomIndex].duration) {
            withAnimation(.easeInOut(duration: 0.5)) {
                sparkles[randomIndex].opacity = 0.0
                sparkles[randomIndex].scale = 0.0
            }
        }
    }
}

struct SparkleData {
    let id = UUID()
    let position: CGPoint
    let size: CGFloat
    var opacity: Double
    var scale: Double
    let duration: Double
}

// MARK: - 预览

#Preview {
    ContentView()
}
